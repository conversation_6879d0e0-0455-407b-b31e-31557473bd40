import { ResourceNotFound } from '../../framework/errors/resource.errors';
import type { UserRepository } from '../../domain/user/user.repository';
import type { JWTService } from '../../domain/token/jwt-token.service';
import { UserRole } from '../../domain/identity/identity';
import type { AsyncUseCase } from '../../framework/use-case/async.use-case';

type Input = {
  userId: string;
};

type Output = string;

export type ImpersonateUserUseCase = AsyncUseCase<Input, Output>;

export const impersonateUserUseCaseFactory = (
  userRepository: UserRepository,
  tokenService: JWTService,
): ImpersonateUserUseCase => async (input: Input): Promise<Output> => {
  const user = await userRepository.getUserByID(input.userId);
  
  if (!user) {
    throw new ResourceNotFound('User not found');
  }

  const token = tokenService.sign(
    {
      userID: user.id,
      role: UserRole.USER,
      phoneNumber: user.phoneNumber,
    },
    user.passwordCode,
  );

  return token;
};

import { IdfyClient } from '@idfy/sdk';
import aws from 'aws-sdk';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';
import type { FastifyInstance } from 'fastify';
import type { OpenAPIV3 } from 'openapi-types';
import { pipe } from 'ramda';
import type { Unleash } from 'unleash-client';
import { initialize, isEnabled } from 'unleash-client';
import type { UnleashConfig } from 'unleash-client/lib/unleash';
import type { ConfigObject } from './config';
import { areaModelInit, setAreaModelReferences } from './domain/area/area.model';
import { areaRepositoryFactory } from './domain/area/area.repository';
import { authenticationServiceFactory } from './domain/authentication/authentication.service';
import { awsS3ServiceFactory } from './domain/aws/aws-s3.service';
import { secretsManagerServiceFactory } from './domain/aws/secrets-manager.service';
import {
  brokerActivityModelInit,
  setBrokerActivityModelReferences,
} from './domain/broker-activity/broker-activity.model';
import { brokerActivityRepositoryFactory } from './domain/broker-activity/broker-activity.repository';
import { brokerEmailVerificationServiceFactory } from './domain/broker/broker-email-verification.service';
import { brokerModelInit } from './domain/broker/broker.model';
import { brokerRepositoryFactory } from './domain/broker/broker.repository';
import { brokerServiceFactory } from './domain/broker/broker.service';
import { employeeMongooseRepositoryFactory } from './domain/broker/mongo/employee.mongoose-repository';
import { contactMongooseRepositoryFactory } from './domain/contact/mongo/contact.mongoose-repository';
import { couponModelInit, setCouponModelReferences } from './domain/coupon/coupon.model';
import { customerServiceFactoryForBrokers } from './domain/customer/customer.service';
import { buyerMongooseRepositoryFactory } from './domain/customer/mongo/buyer/buyer.mogoose-repository';
import { sellerMongooseRepositoryFactory } from './domain/customer/mongo/seller/seller.mongoose-repository';
import { departmentMongooseRepositoryFactory } from './domain/department/department.mongoose-repository';
import {
  createEiendomsverdiRestApiClient,
  createEiendomsverdiSoapApiClient,
} from './domain/eiendomsverdi/EiendomsverdiApiClient';
import {
  eiendomsverdiAuditModelInit,
  setEiendomsverdiModelReferences,
} from './domain/eiendomsverdi/EiendomsverdiAuditModel';
import { eiendomsverdiAuditRepositoryFactory } from './domain/eiendomsverdi/EiendomsverdiAuditRepository';
import { createEiendomsverdiEstateValuationService } from './domain/eiendomsverdi/EiendomsverdiEstateValuationService';
import {
  cacheRequestServiceFactory,
  cacheRestRequestServiceFactory,
} from './domain/eiendomsverdi/eiendomsverdi-cache-request.service';
import { eiendomsverdiEstateCacheModelInit } from './domain/eiendomsverdi/eiendomsverdi-estate-cache.model';
import { eiendomsverdiEstateCacheRepositoryFactory } from './domain/eiendomsverdi/eiendomsverdi-estate-cache.repository';
import { fetchCachedEstateDataFactory } from './domain/eiendomsverdi/eiendomsverdi-estate-cache.service';
import { createElectricityLeadService } from './domain/electricity-providers/electicity-lead.service';
import { estateImageFromUserModelInit } from './domain/estate-image-from-user/estate-image-from-user.model';
import { estateImageFromUserRepositoryFactory } from './domain/estate-image-from-user/estate-image-from-user.repository';
import { estatePriceHistoryModelInit } from './domain/estate-price-histories/estate-price-histories.model';
import { estatePriceHistoriesRepositoryFactory } from './domain/estate-price-histories/estate-price-histories.repository';
import {
  estateChecklistConnectionModelInit,
  setEstateChecklistConnectionModelReferences,
} from './domain/estate/estate-checklist-connection/estate-checklist-connection.model';
import { estateChecklistConnectionRepositoryFactory } from './domain/estate/estate-checklist-connection/estate-checklist-connection.repository';
import {
  estateChecklistModelInit,
  setEstateChecklistModelReferences,
} from './domain/estate/estate-checklist/estate-checklist.model';
import { estateChecklistRepositoryFactory } from './domain/estate/estate-checklist/estate-checklist.repository';
import { estateChecklistServiceFactory } from './domain/estate/estate-checklist/estate-checklist.service';
import {
  estateEventConnectionModelInit,
  setEstateEventConnectionModelReferences,
} from './domain/estate/estate-event-connection/estate-event-connection.model';
import { estateEventConnectionRepositoryFactory } from './domain/estate/estate-event-connection/estate-event-connection.repository';
import { estateEventModelInit } from './domain/estate/estate-event/estate-event.model';
import { estateEventRepositoryFactory } from './domain/estate/estate-event/estate-event.repository';
import { estateEventServiceFactory } from './domain/estate/estate-event/estate-event.service';
import type { StatisticsService } from './domain/estate/estate-statistics/statistics.service';
import { statisticsServiceFactory } from './domain/estate/estate-statistics/statistics.service';
import { estateSyncServiceFactory } from './domain/estate/estate-sync/estate-sync.service';
import { estateTimelineBuyerServiceFactory } from './domain/estate/estate-timeline/buyer/estate-timeline-buyer.service';
import { estateTimelineServiceFactory } from './domain/estate/estate-timeline/estate-timeline.service';
import { estateTimelineSellerServiceFactory } from './domain/estate/estate-timeline/seller/estate-timeline-seller.service';
import { estateTodoModelInit, setEstateTodoModelReferences } from './domain/estate/estate-todo/estate-todo.model';
import { estateTodoRepositoryFactory } from './domain/estate/estate-todo/estate-todo.repository';
import { estateModelInit, setEstateModelReferences } from './domain/estate/estate.model';
import { estateRepositoryFactory } from './domain/estate/estate.repository';
import { estateServiceFactory } from './domain/estate/estate.service';
import { estateMongooseRepositoryFactory } from './domain/estate/mongo/estate.mogoose-repository';
import { vitecEstateExtensionsModelInit } from './domain/estate/vitec-estate-extension/vitec-estate-extension.model';
import { vitecEstateExtensionRepositoryFactory } from './domain/estate/vitec-estate-extension/vitec-estate-repository';
import { wealthManagementServiceFactory } from './domain/estate/wealth-management/wealth-management.service';
import { externalLeadAuditModelInit } from './domain/external-lead-audit/external-lead-audit.model';
import { ExternalLeadAuditRepositoryFactory } from './domain/external-lead-audit/external-lead-audit.repository';
import { favoriteModelInit, setFavoriteModelReferences } from './domain/favorites/favorite.model';
import { favoriteRepositoryFactory } from './domain/favorites/favorites.repository';
import { feedModelInit, setFeedModelReferences } from './domain/feed/feed.model';
import { feedRepositoryFactory } from './domain/feed/feed.repository';
import { fileModelInit } from './domain/file/file.model';
import { fileRepoFactory } from './domain/file/file.repo';
import { fileServiceFactory } from './domain/file/file.service';
import { hjemNoServiceFactory } from './domain/hjem-no/hjem-no.service';
import { htmlToPdf } from './domain/html-to-pdf/html-to-pdf';
import type { WithOptionalIdentity, WithoutIdentity } from './domain/identity/identity';
import { kokkelorenServiceFactory } from './domain/kokkeloren/kokkeloren.service';
import { leadAuditModelInit, setLeadAuditModelReferences } from './domain/leads/lead-audit.model';
import { leadAuditRepositoryFactory } from './domain/leads/lead-audit.repository';
import { leadsServiceFactory } from './domain/leads/leads.service';
import { lipscoreBrokerRatingRepositoryFactory } from './domain/lipscore-broker-rating/lipscore-broker-rating.repo';
import { lipscoreBrokerRatingServiceFactory } from './domain/lipscore-broker-rating/lipscore-broker-rating.service';
import { mailAuditModelInit } from './domain/mail/mail-audit.model';
import { mailAuditRepositoryFactory } from './domain/mail/mail-audit.repository';
import { mailServiceFactory } from './domain/mail/mail-service';
import { mapboxServiceFactory } from './domain/mapbox/mapbox.service';
import { statusOATag } from './domain/oa-tag/status.oa-tag';
import { ServiceOfferViaEmailType } from './domain/offers/service-offer';
import { serviceOfferServiceFactory } from './domain/offers/service-offer.service';
import {
  overtakeProtocolMeterModelInit,
  setOvertakeProtocolMeterModelReferences,
} from './domain/otp/otp-meter/otp-meter.model';
import { otpMeterRepoFactory } from './domain/otp/otp-meter/otp-meter.repo';
import {
  overtakeProtocolParticipantModelInit,
  setOvertakeProtocolParticipantModelReferences,
} from './domain/otp/otp-participant/otp-participant.model';
import { otpParticipantRepoFactory } from './domain/otp/otp-participant/otp-participant.repo';
import { overtakeProtocolModelInit } from './domain/otp/otp.model';
import { overtakeProtocolRepositoryFactory } from './domain/otp/otp.repository';
import { otpServiceFactory } from './domain/otp/otp.service';
import { passwordHelperFactory } from './domain/password/password.helper';
import { pepFormParticipantModelInit } from './domain/pep-form-participant/pep-form-participant.model';
import { pepFormParticipantRepositoryFactory } from './domain/pep-form-participant/pep-form-participant.repository';
import { pepFormModelInit, setPEPFormModelReferences } from './domain/pep-form/pep-form.model';
import { pepFormRepositoryFactory } from './domain/pep-form/pep-form.repository';
import { popupModelInit, setPopupModelReferences } from './domain/popup/popup.model';
import { popupRepositoryFactory } from './domain/popup/popup.repository';
import { popupServiceFactory } from './domain/popup/popup.service';
import {
  priceGuessingEstateModelInit,
  setPriceGuessingEstateModelReferences,
} from './domain/price-guessing-estate/price-guessing-estate.model';
import { priceGuessingEstateRepositoryFactory } from './domain/price-guessing-estate/price-guessing-estate.repository';
import { priceGuessingUserGuessModelInit } from './domain/price-guessing-user-guess/price-guessing-user-guess.model';
import { priceGuessingUserGuessRepositoryFactory } from './domain/price-guessing-user-guess/price-guessing-user-guess.repository';
import { proxyMongooseRepositoryFactory } from './domain/proxy/mongo/proxy.mongoose-repository';
import { dismissedActionsModelInit } from './domain/recommendation-engine/dismiss/dismissed-actions.model';
import { dismissedActionsRepositoryFactory } from './domain/recommendation-engine/dismiss/dismissed-actions.repository';
import {
  referralCodesModelInit,
  setReferralCodesModelReferences,
} from './domain/referral/referral-codes/referral-code.model';
import {
  referralInvitesModelInit,
  setReferralInvitesModelReferences,
} from './domain/referral/referral-invites/referral-invites.model';
import {
  referralRewardsModelInit,
  setReferralRewardsModelReferences,
} from './domain/referral/referral-rewards/referral-reward.model';
import { referralRewardRepositoryFactory } from './domain/referral/referral-rewards/referral-reward.repository';
import { referralRepositoryFactory } from './domain/referral/referral.repository';
import { createReferralService } from './domain/referral/referral.service';
import { rewardTypesModelInit, setRewardTypesModelReferences } from './domain/referral/reward-types/reward-types.model';
import { sectorAlarmServiceFactory } from './domain/sector-alarm/sector-alarm.service';
import { settlementBuyerEquityModelInit } from './domain/settlement-buyer/settlement-buyer-equity.model';
import { settlementBuyerLoanModelInit } from './domain/settlement-buyer/settlement-buyer-loan.model';
import {
  setSettlementBuyerParticipantModelReferences,
  settlementBuyerParticipantModelInit,
} from './domain/settlement-buyer/settlement-buyer-participant.model';
import { settlementBuyerParticipantRepositoryFactory } from './domain/settlement-buyer/settlement-buyer-participant.repository';
import {
  setSettlementBuyerModelReferences,
  settlementBuyerModelInit,
} from './domain/settlement-buyer/settlement-buyer.model';
import { settlementBuyerRepositoryFactory } from './domain/settlement-buyer/settlement-buyer.repository';
import { settlementSellerAccountModelInit } from './domain/settlement-seller/settlement-seller-account.model';
import { settlementSellerLoanModelInit } from './domain/settlement-seller/settlement-seller-loan.model';
import { settlementSellerParticipantModelInit } from './domain/settlement-seller/settlement-seller-participant.model';
import {
  setSettlementSellerModelReferences,
  settlementSellerModelInit,
} from './domain/settlement-seller/settlement-seller.model';
import { settlementSellerRepositoryFactory } from './domain/settlement-seller/settlement-seller.repository';
import { settlementServiceFactory } from './domain/settlement/settlement.service';
import type { WithLeadSendingParams } from './domain/slack/slack-message-attachment.service';
import { SellerOrBuyer, slackServiceFactory } from './domain/slack/slack.service';
import { smsAuditModelInit } from './domain/sms/sms-audit.model';
import { smsAuditRepositoryFactory } from './domain/sms/sms-audit.repository';
import { smsServiceFactory } from './domain/sms/sms-service';
import { steddyServiceFactory } from './domain/steddy/steddy.service';
import { storebrandAuditModelInitTamo } from './domain/storebrand-vitec/storebrand-audit.model';
import { storebrandVitecAuditRepositoryFactory } from './domain/storebrand-vitec/storebrand-audit.repository';
import { storebrandVitecServiceFactory } from './domain/storebrand-vitec/storebrand.service';
import { storebrandAuditModelInit } from './domain/storebrand/storebrand-audit.model';
import { storebrandAuditRepositoryFactory } from './domain/storebrand/storebrand-audit.repository';
import { storebrandServiceFactory } from './domain/storebrand/storebrand.service';
import { telenorServiceFactory } from './domain/telenor/telenor.service';
import { overtakeProtocolOfEmptyEstateDocumentServiceFactory } from './domain/templates/document-templates/overtake-protocol/overtake-protocol-of-empty-estate.document.service';
import { overtakeProtocolDocumentServiceFactory } from './domain/templates/document-templates/overtake-protocol/overtake-protocol.document.service';
import { politicallyExposedPersonDocumentServiceFactory } from './domain/templates/document-templates/politically-exposed-person/pep.document.service';
import { settlementBuyerDocumentServiceFactory } from './domain/templates/document-templates/settlement/settlement-buyer/settlement-buyer.document.service';
import { settlementSellerDocumentServiceFactory } from './domain/templates/document-templates/settlement/settlement-seller/settlement-seller.document.service';
import { jwtTokenServiceFactory } from './domain/token/jwt-token.service';
import type { TokenService } from './domain/token/token.service';
import { urlModelInit } from './domain/url/url.model';
import { urlRepositoryFactory } from './domain/url/url.repository';
import { setUserActivityModelReferences, userActivityModelInit } from './domain/user-activity/user-activity.model';
import { userActivityRepositoryFactory } from './domain/user-activity/user-activity.repository';
import { setUserAreaModelReferences, userAreaModelInit } from './domain/user-area/user-area.model';
import { userAreaRepositoryFactory } from './domain/user-area/user-area.repository';
import {
  setUserFirebaseTokenModelReferences,
  userFirebaseTokenModelInit,
} from './domain/user-ios-token/user-firebase-token.model';
import { userFirebaseTokenRepositoryFactory } from './domain/user-ios-token/user-firebase-token.repository';
import { setUserOptionsModelReferences, userOptionsModelInit } from './domain/user-options/user-options.model';
import { userOptionsRepositoryFactory } from './domain/user-options/user-options.repository';
import { deleteUserModelInit } from './domain/user/deleteUser.model';
import { RegisteredWith } from './domain/user/user';
import { setUserModelReferences, userModelInit } from './domain/user/user.model';
import { userRepositoryFactory } from './domain/user/user.repository';
import type { ValuationService } from './domain/valuation/ValuationService';
import { vippsServiceFactory } from './domain/vipps/vipps.service';
import { adminAuthEndpointFactory } from './endpoints/admin/admin-auth.endpoint';
import { adminInterfaceEndpointFactory } from './endpoints/admin/admin-interface.endpoint';
import { impersonateUserEndpointFactory } from './endpoints/admin/impersonate-user.endpoint';
import { searchUsersEndpointFactory } from './endpoints/admin/search-users.endpoint';
import { getAreasEndpointFactory } from './endpoints/area/get-areas.endpoint';
import { bankIdIframeEndpointFactory } from './endpoints/bankid/bankid-iframe-endpoint';
import { bankIdSessionEndpointFactory } from './endpoints/bankid/bankid-session-endpoint';
import { verifyBrokerEmailEndpointFactory } from './endpoints/broker/email/verify-broker-email.endpoint';
import { getBrokerCustomerEndpointFactory } from './endpoints/broker/get-broker-customer.endpoint';
import { getBrokerEstatePotentialBuyersFactory } from './endpoints/broker/get-broker-estate-potential-buyers.endpoint';
import { getBrokerEstatesEndpointFactory } from './endpoints/broker/get-broker-estates.endpoint';
import { getBrokerFormsForEstatesEndpointFactory } from './endpoints/broker/get-broker-forms-for-estates.endpoint';
import { getBrokerLeadsEndpointFactory } from './endpoints/broker/get-broker-leads.endpoint';
import { getBrokerStatsEndpointFactory } from './endpoints/broker/get-broker-stats.endpoint';
import { getBrokerEndpointFactory } from './endpoints/broker/get-broker.endpoint';
import { getBuyersEndpointFactory } from './endpoints/broker/get-buyers.endpoint';
import { getEstateTimelineBrokerEndpointFactory } from './endpoints/broker/get-customer-timeline.endpoint';
import { getOwnersEndpointFactory } from './endpoints/broker/get-owners.endpoint';
import { getSellersEndpointFactory } from './endpoints/broker/get-sellers.endpoint';
import { changeBrokerPasswordEndpointFactory } from './endpoints/broker/password/change-broker-password.endpoint';
import { resetBrokerPasswordEndpointFactory } from './endpoints/broker/password/reset-broker-password.endpoint';
import { updateBrokerPasswordEndpointFactory } from './endpoints/broker/password/update-broker-password.endpoint';
import { getConsentEndpointFactory } from './endpoints/consent/get-consent.endpoint';
import { updateConsentEndpointFactory } from './endpoints/consent/update-consent.endpoint';
import { updateConsentWithNordvikBoligCallbackEndpointFactory } from './endpoints/consent/update-nordvikbolig-consent.endpoint';
import { downloadDocumentEndpointFactory } from './endpoints/documents/download-document.endpoint';
import { getSignableEstateDocumentsEndpointFactory } from './endpoints/documents/get-signable-estate-documents.endpoint';
import { getSignedUrlEndpointFactory } from './endpoints/documents/get-signed-url.endpoint';
import { getSignableUserDocumentCountEndpointFactory } from './endpoints/documents/get-user-document.endpoint';
import { getEstateDocumentsEndpointFactory } from './endpoints/documents/get-user-estate-documents.endpoint';
import { headDownloadDocumentEndpointFactory } from './endpoints/documents/head-download-document.endpoint';
import { discussWIthBrokerEndpointFactory } from './endpoints/ekstra/discuss-with-broker.endpoint';
import { getEkstraStatisticsEndpointFactory } from './endpoints/ekstra/get-ekstra-statistics.endpoint';
import { orderEkstraEndpointFactory } from './endpoints/ekstra/order-ekstra.endpoint';
import { startSalesProcessEndpointFactory } from './endpoints/ekstra/start-sales-process.endpoint';
import { emailTestEndpointFactory } from './endpoints/email/email-test.endpoint';
import { addFavoriteEndpointFactory } from './endpoints/estates/add-favorite.endpoint';
import { createEstateEndpointFactory } from './endpoints/estates/create-estate.endpoint';
import { deleteFavoriteEndpointFactory } from './endpoints/estates/delete-favorites.endpoint';
import { getEstateBuyerChecklistsEndpointFactory } from './endpoints/estates/estate-checklists/get-estate-buyer-checklist.endpoint';
import { getEstateChecklistsEndpointFactory } from './endpoints/estates/estate-checklists/get-estate-checklists.endpoint';
import { toggleEstateBuyerChecklistTodoEndpointFactory } from './endpoints/estates/estate-checklists/toggle-estate-buyer-checklist.endpoint';
import { toggleEstateChecklistTodoEndpointFactory } from './endpoints/estates/estate-checklists/toggle-estate-checklist-todo.endpoint';
import { setEstateEventDateEndpointFactory } from './endpoints/estates/estate-event/set-estate-event-date.endpoint';
import { getEstateImageFromUserEndpointFactory } from './endpoints/estates/estate-image-from-user/get-estate-image-from-user.endpoint';
import { upsertEstateImageFromUserEndpointFactory } from './endpoints/estates/estate-image-from-user/upsert-estate-image-from-user.endpoint';
import { getEstatePriceHistoryEndpointFactory } from './endpoints/estates/estate-price-history/get-estate-price-history.endpoint';
import { getEstateStatisticsEndpointFactory } from './endpoints/estates/estate-statistics/get-estate-statistics.endpoint';
import { estateSyncBankIdForceEndpointFactory } from './endpoints/estates/estate-sync/estate-sync-bankid-force.endpoint';
import { estateSyncBankIdEndpointFactory } from './endpoints/estates/estate-sync/estate-sync-bankid.endpoint';
import { estateSyncVippsEndpointFactory } from './endpoints/estates/estate-sync/estate-sync-vipps.endpoint';
import { getEstateTimelineBucketEndpointFactory } from './endpoints/estates/estate-timeline/get-estate-timeline-bucket.endpoint';
import { getEstateTimelineTodoEndpointFactory } from './endpoints/estates/estate-timeline/get-estate-timeline-todo.endpoint';
import { getEstateTimelineEndpointFactory } from './endpoints/estates/estate-timeline/get-estate-timeline.endpoint';
import { getEstateBuyersEndpointFactory } from './endpoints/estates/get-estate-buyers.endpoint';
import { getEstateSalesEndpointFactory } from './endpoints/estates/get-estate-sales.endpoint';
import { getEstateTimelineBuyerEndpointFactory } from './endpoints/estates/get-estate-timeline-buyer.endpoint';
import { getEstateTimelineSellerEndpointFactory } from './endpoints/estates/get-estate-timeline-seller.endpoint';
import { getEstateValuationEndpointFactory } from './endpoints/estates/get-estate-valuation.endpoint';
import { getEstateEndpointFactory } from './endpoints/estates/get-estate.endpoint';
import { getEstatesValuationEndpointFactory } from './endpoints/estates/get-estates-valuation.endpoint';
import { getEstatesEndpointFactory } from './endpoints/estates/get-estates.endpoint';
import { getPremarketEstatesEndpointFactory } from './endpoints/estates/get-premarket-estates.endpoint';
import { getFavoritePremarketEstatesEndpointFactory } from './endpoints/estates/get-premarket-favorites.endpoint';
import { updateEstateEndpointFactory } from './endpoints/estates/update-estate.endpoint';
import { getExternalLeadAuditEndpointFactory } from './endpoints/external-lead-audit/external-lead-audit.endpoint';
import { getFeatureFlagEndpointFactory } from './endpoints/feature-flags/get-feature-flag.endpoint';
import { deleteFeedEndpointFactory } from './endpoints/feed/delete-feed-entry.endpoint';
import { listFeedEndpointFactory } from './endpoints/feed/list-feed.endpoint';
import { getFinancialDetailsEndpointFactory } from './endpoints/financial-details/get-financial-details.endpoint';
import { updateFinancialDetailsEndpointFactory } from './endpoints/financial-details/update-financial-details.endpoint';
import { createByggstartLeadEndpointFactory } from './endpoints/leads/create-byggstart-lead.endpoint';
import { createElectricityLeadEndpointFactory } from './endpoints/leads/create-electricity-lead.endpoint';
import { createExponovaLeadEndpointFactory } from './endpoints/leads/create-exponova-lead.endpoint';
import { createSectorAlarmLeadEndpointFactory } from './endpoints/leads/create-sector-alarm-lead.endpoint';
import { createStorebrandLeadEndpointFactory } from './endpoints/leads/create-storebrand-lead.endpoint';
import { createTelenorOfferEndpointFactory } from './endpoints/leads/create-telenor-offer.endpoint';
import { createVitecLeadEndpointFactory } from './endpoints/leads/create-vitec-lead.endpoint';
import { isSteddyAvailableEndpointFactory } from './endpoints/leads/is-steddy-available';
import { getLipscoreBrokerRatingAverageEndpointFactory } from './endpoints/lipscore-broker-rating/get-lipscore-broker-rating-average.endpoint';
import { brokerLoginEndpointFactory } from './endpoints/login/broker-login-endpoint';
import { loginEndpointFactory } from './endpoints/login/login-endpoint';
import { vippsGetInfoEndpointFactory } from './endpoints/login/vipps-get-info.endpoint';
import { vippsLoginEndpointFactory } from './endpoints/login/vipps-login.endpoint';
import { createOtpEndpointFactory } from './endpoints/otp/create-otp.endpoint';
import { createOtpDocumentEndpointFactory } from './endpoints/otp/document/create-otp-document.endpoint';
import { getOtpDocumentEndpointFactory } from './endpoints/otp/document/get-otp-document.endpoint';
import { sendOtpSignSmsEndpointFactory } from './endpoints/otp/document/send-otp-sign-sms.endpoint';
import { finalizeOtpEndpointFactory } from './endpoints/otp/finalize-otp.endpoint';
import { getOtpEndpointFactory } from './endpoints/otp/get-otp.endpoint';
import { createOtpMeterEndpointFactory } from './endpoints/otp/meter/create-otp-meter.endpoint';
import { deleteOtpMeterEndpointFactory } from './endpoints/otp/meter/delete-otp-meter.endpoint';
import { getOtpMeterImageEndpointFactory } from './endpoints/otp/meter/get-otp-meter-image.endpoint';
import { updateOtpMeterEndpointFactory } from './endpoints/otp/meter/update-otp-meter.endpoint';
import { createOtpParticipantEndpointFactory } from './endpoints/otp/participant/create-otp-participant.endpoint';
import { deleteOtpParticipantEndpointFactory } from './endpoints/otp/participant/delete-otp-participant.endpoint';
import { getOtpParticipantImageEndpointFactory } from './endpoints/otp/participant/get-otp-participant-image.endpoint';
import { updateOtpParticipantEndpointFactory } from './endpoints/otp/participant/update-otp-participant.endpoint';
import { resetOtpFormEndpointFactory } from './endpoints/otp/reset-otp-form.endpoint';
import { updateOtpEndpointFactory } from './endpoints/otp/update-otp.endpoint';
import { createIdfyPEPDocumentEndpointFactory } from './endpoints/pep-form/create-idfy-pep-document.endpoint';
import { createPEPFormEndpointFactory } from './endpoints/pep-form/create-pep-form.endpoint';
import { finalizePEPEndpointFactory as finalizePEPNewEndpointFactory } from './endpoints/pep-form/finalize-pep.endpoint';
import { getPEPDocumentSignStatusEndpointFactory as getPEPDocumentSignStatusNewEndpointFactory } from './endpoints/pep-form/get-pep-document-sign-status.endpoint';
import { getPEPFormParticipantsEndpointFactory } from './endpoints/pep-form/get-pep-form-participants.endpoint';
import { getPEPFormEndpointFactory as getPEPFormNewEndpointFactory } from './endpoints/pep-form/get-pep-form.endpoint';
import { resetPepFormEndpointFactory } from './endpoints/pep-form/reset-pep-form.endpoint';
import { updatePEPFormParticipantEndpointFactory } from './endpoints/pep-form/update-pep-form-participant.endpoint';
import { updatePEPFormEndpointFactory as updatePEPFormNewEndpointFactory } from './endpoints/pep-form/update-pep-form.endpoint';
import { getPopupEndpointFactory } from './endpoints/popup/get-popup.endpoint';
import { updatePopupEndpointFactory } from './endpoints/popup/update-popup.endpoint';
import { getPriceGuessingDailyEstateEndpointFactory } from './endpoints/price-guessing-estate/get-price-guessing-daily-estate.endpoint';
import { getPriceGuessingReportEndpointFactory } from './endpoints/price-guessing-estate/get-price-guessing-report.endpoints';
import { getPriceGuessingScoreboardEndpointFactory } from './endpoints/price-guessing-estate/get-price-guessing-scoreboard.endpoint';
import { getTotalGuessesTodayByUserIdEndpointFactory } from './endpoints/price-guessing-estate/get-total-guesses-today-by-userId.endpoint';
import { getUserIdsWhoFinishedGuessingForTodayEndpointFactory } from './endpoints/price-guessing-estate/get-user-ids-who-finished-guessing-for-today.endpoint';
import { getUserStatisticsEndpointFactory } from './endpoints/price-guessing-estate/get-user-statistics.endpoints';
import { guessEstatePriceEndpointFactory } from './endpoints/price-guessing-estate/guess-estate-price.endpoint';
import { getAllRewardsEndpointFactory } from './endpoints/referral/get-all-rewards.endpoint';
import { updateRewardEndpointFactory } from './endpoints/referral/update-reward.endpoint';
import { canRegisterEndpointFactory } from './endpoints/register/can-register.endpoint';
import { registerBankIdEndpointFactory } from './endpoints/register/register-bankid-endpoint';
import { registerBrokerEndpointFactory } from './endpoints/register/register-broker-endpoint';
import { registerEndpointFactory } from './endpoints/register/register-endpoint';
import { vippsRegisterEndpointFactory } from './endpoints/register/vipps-register.endpoint';
import { deleteRecommenddedNextActionEndpointFactory } from './endpoints/reminder/delete-recommended-next-action.endpoint';
import { getIfInsuranceOfferEndpointFactory } from './endpoints/services/get-if-insurance-offer.endpoint';
import { getOfferEndpointFactory } from './endpoints/services/get-offer.endpoint';
import { getSsgOfferEndpointFactory } from './endpoints/services/get-ssg-offer.endpoint';
import { getSteddyOfferEndpointFactory } from './endpoints/services/get-steddy-offer.endpoint';
import { getStorebrandOfferEndpointFactory } from './endpoints/services/get-storebrand-offer.endpoint';
import { getTryggOfferEndpointFactory } from './endpoints/services/get-trygg-offer.endpoint';
import { getSmsVerificationEndpointFactory } from './endpoints/services/request-sms-verification.endpoint';
import { verifySmsCodeEndpointFactory } from './endpoints/services/verify-sms-code.endpoint';
import { createSettlementBuyerEndpointFactory } from './endpoints/settlement-buyer/create-settlement-buyer.endpoint';
import { deleteSettlementBuyerEndpointFactory } from './endpoints/settlement-buyer/delete-settlement-buyer.endpoint';
import { createSettlementBuyerDocumentEndpointFactory } from './endpoints/settlement-buyer/document/create-settlement-buyer-document.endpoint';
import { getSettlementBuyerDocumentSignStatusEndpointFactory } from './endpoints/settlement-buyer/document/get-settlement-buyer-document-sign-status.endpoint';
import { sendSettlementBuyerSignSmsEndpointFactory } from './endpoints/settlement-buyer/document/send-settlement-buyer-sign-sms.endpoint';
import { finalizeSettlementBuyerEndpointFactory } from './endpoints/settlement-buyer/finalize-settlement-buyer.endpoint';
import { getSettlementBuyerEndpointFactory } from './endpoints/settlement-buyer/get-settlement-buyer.endpoint';
import { updateSettlementBuyerParticipantEndpointFactory } from './endpoints/settlement-buyer/participant/update-settlement-buyer-participant.endpoint';
import { resetSettlementBuyerFormEndpointFactory } from './endpoints/settlement-buyer/reset-settlement-buyer-form.endpoint';
import { updateSettlementBuyerEndpointFactory } from './endpoints/settlement-buyer/update-settlement-buyer.endpoint';
import { updateSettlementSellerAccountsEndpointFactory } from './endpoints/settlement-seller/account/update-settlement-seller-accounts.endpoint';
import { createSettlementSellerEndpointFactory } from './endpoints/settlement-seller/create-settlement-seller.endpoint';
import { deleteSettlementSellerEndpointFactory } from './endpoints/settlement-seller/delete-settlement-seller.endpoint';
import { createSettlementSellerDocumentEndpointFactory } from './endpoints/settlement-seller/document/create-settlement-seller-document.endpoint';
import { getSettlementSellerDocumentSignStatusEndpointFactory } from './endpoints/settlement-seller/document/get-settlement-seller-document-sign-status.endpoint';
import { sendSettlementSellerSignSmsEndpointFactory } from './endpoints/settlement-seller/document/send-settlement-seller-sign-sms.endpoint';
import { finalizeSettlementSellerEndpointFactory } from './endpoints/settlement-seller/finalize-settlement-seller.endpoint';
import { getSettlementSellerEndpointFactory } from './endpoints/settlement-seller/get-settlement-seller.endpoint';
import { updateSettlementSellerLoansEndpointFactory } from './endpoints/settlement-seller/loan/update-settlement-seller-loans.endpoint';
import { resetSettlementSellerFormEndpointFactory } from './endpoints/settlement-seller/reset-settlement-seller-form.endpoint';
import { updateSettlementSellerEndpointFactory } from './endpoints/settlement-seller/update-settlement-seller.endpoint';
import { authenticatedStatusEndpointFactory } from './endpoints/status/authenticated-status.endpoint';
import { statusEndpointFactory } from './endpoints/status/status-endpoint';
import { getTimelineBucketEndpointFactory } from './endpoints/timeline/get-timeline-bucket.endpoint';
import { getTimelineEndpointFactory } from './endpoints/timeline/get-timeline.endpoint';
import { createShortUrlEndpointFactory } from './endpoints/url/create-short-url.endpoint';
import { getShortUrlEndpointFactory } from './endpoints/url/get-short-url.endpoint';
import { deleteUserAndroidTokenEndpointFactory } from './endpoints/user/delete-user-android-token.endpoint';
import { deleteUserIosTokenEndpointFactory } from './endpoints/user/delete-user-ios-token.endpoint';
import { deleteUserEndpointFactory } from './endpoints/user/delete-user.endpoint';
import { getUserEndpointFactory } from './endpoints/user/get-user.endpoint';
import { createGoogleAnalyticsEventEndpointFactory } from './endpoints/user/google-analytics/create-google-analytics-event.endpoint';
import { changeUserPasswordEndpointFactory } from './endpoints/user/password/change-user-password.endpoint';
import { resetUserPasswordEndpointFactory } from './endpoints/user/password/reset-user-password.endpoint';
import { updateUserPasswordEndpointFactory } from './endpoints/user/password/update-user-password.endpoint';
import { setUserAndroidTokenEndpointFactory } from './endpoints/user/set-user-android-token.endpoint';
import { setUserIosTokenEndpointFactory } from './endpoints/user/set-user-ios-token.endpoint';
import { updateUserEmailEndpointFactory } from './endpoints/user/update-email.endpoint';
import { updateUserEndpointFactory } from './endpoints/user/update-user.endpoint';
import type { Endpoint } from './framework/endpoint/endpoint';
import type { Request, Response } from './framework/endpoint/request-response';
import { Unauthorized } from './framework/errors/authentication.errors';
import { BadRequest } from './framework/errors/bad-request.error';
import { UnprocessableEntity } from './framework/errors/unprocessable-entity.errors';
import { fastifyServerFactory } from './framework/fastify/fastify-server.factory';
import { fastifySwaggerFactory } from './framework/fastify/fastify-swagger-factory';
import type { IdfyService } from './framework/idfy/idfy-service';
import { idfyServiceFactory } from './framework/idfy/idfy-service';
import { mongooseConnectionFactory } from './framework/mongoose/mongoose-connection';
import { sequelizeConnection } from './framework/sequelize/connection';
import type { AsyncUseCase } from './framework/use-case/async.use-case';
import type { VitecService } from './framework/vitec/vitec-service';
import { vitecServiceFactory } from './framework/vitec/vitec-service';
import type { Logger } from './logger';
import { brokerLevelUseCaseFactory } from './use-cases/_authentication/broker-level.use-case';
import type { WithApiKey } from './use-cases/apikey-authenticated.use-case';
import { apikeyAuthenticatedUseCaseFactory } from './use-cases/apikey-authenticated.use-case';
import { getAreasUseCaseFactory } from './use-cases/area/get-areas.use-case';
import type { WithToken } from './use-cases/authenticated.use-case';
import { authenticatedUseCaseFactory } from './use-cases/authenticated.use-case';
import { bankIdIframeUseCaseFactory } from './use-cases/bankid/bankid-iframe.use-case';
import { bankIdSessionUseCaseFactory } from './use-cases/bankid/bankid-session.use-case';
import { resetBrokerPasswordUseCaseFactory } from './use-cases/bankid/password/reset-broker-password.use-case';
import { updateBrokerPasswordUseCaseFactory } from './use-cases/bankid/password/update-broker-password.use-case';
import { changeBrokerPasswordUseCaseFactory } from './use-cases/broker/change-broker-password.use-case';
import { verifyBrokerEmailUseCaseFactory } from './use-cases/broker/email/verify-broker-email.use-case';
import { getBrokerCustomerUseCaseFactory } from './use-cases/broker/get-broker-customer.use-case';
import { getBrokerEstatePotentialBuyersUseCaseFactory } from './use-cases/broker/get-broker-estate-potential-buyers.use-case';
import { getBrokerEstatesUseCaseFactory } from './use-cases/broker/get-broker-estates.use-case';
import { getBrokerFormsForEstatesUseCaseFactory } from './use-cases/broker/get-broker-forms-for-estates.use-case';
import { getBrokerUseCaseFactory } from './use-cases/broker/get-broker.use-case';
import { getBuyersUseCaseFactory } from './use-cases/broker/get-buyers.use-case';
import { getBrokerLeadsUseCaseFactory } from './use-cases/broker/get-leads.use-case';
import { getOwnersUseCaseFactory } from './use-cases/broker/get-owners.use-case';
import { getSellersUseCaseFactory } from './use-cases/broker/get-sellers.use-case';
import { getUserTimelineSellerUseCaseFactory } from './use-cases/broker/get-user-timeline.use-case';
import { getConsentUseCaseFactory } from './use-cases/consent/get-consent.use-case';
import { updateConsentUseCaseFactory } from './use-cases/consent/update-consent.use-case';
import { downloadDocumentUseCaseFactory } from './use-cases/documents/download-document.use-case';
import { getCompletedEstateDocumentsUseCaseFactory } from './use-cases/documents/get-completed-estate-documents.use-case';
import { getSignableEstateDocumentsUseCaseFactory } from './use-cases/documents/get-signable-estate-documents.use-case';
import { getSignableUserDocumentCountUseCaseFactory } from './use-cases/documents/get-signable-user-document-count.use-case';
import { getSignedUrlUseCaseFactory } from './use-cases/documents/get-signed-url.use-case';
import { discussWithBrokerUseCaseFactory } from './use-cases/ekstra/discuss-with-broker-use-case';
import { getEkstraStatisticsUseCaseFactory } from './use-cases/ekstra/get-ekstra-statistics.use-case';
import { orderEkstraUseCaseFactory } from './use-cases/ekstra/order-ekstra-use-case';
import { startSalesProcessUseCaseFactory } from './use-cases/ekstra/start-sales-process-use-case';
import { emailTestUseCaseFactory } from './use-cases/email/email-test.use-case';
import { addFavoriteUseCaseFactory } from './use-cases/estate/add-favorite.use-case';
import { createEstateUseCaseFactory } from './use-cases/estate/create-estate.use-case';
import { deleteFavoriteUseCaseFactory } from './use-cases/estate/delete-favorite.use-case';
import { getEstateChecklistsUseCaseFactory } from './use-cases/estate/estate-checklist/get-estate-checklist.use-case';
import { toggleEstateChecklistTodoUseCaseFactory } from './use-cases/estate/estate-checklist/toggle-estate-checklist.use-case';
import { setEstateEventDateUseCaseFactory } from './use-cases/estate/estate-event/set-estate-event-date.use-case';
import { getEstateImageFromUserUseCaseFactory } from './use-cases/estate/estate-image-from-user/get-estate-image-from-user-url.use-case';
import { updateEstateImageFromUserUseCaseFactory } from './use-cases/estate/estate-image-from-user/update-estate-image-from-user.use-case';
import { getEstatePriceHistoryUseCaseFactory } from './use-cases/estate/estate-price-history/get-estate-price-history.use-case';
import { getEstateStatisticsUseCaseFactory } from './use-cases/estate/estate-statistics/get-estate-statistics.use-case';
import { estateSyncBankIdForceUseCaseFactory } from './use-cases/estate/estate-sync/estate-sync-bankid-force.use-case';
import { estateSyncBankIdUseCaseFactory } from './use-cases/estate/estate-sync/estate-sync-bankid.use-case';
import { estateSyncVippsUseCaseFactory } from './use-cases/estate/estate-sync/estate-sync-vipps.use-case';
import { getEstateTimelineBucketUseCaseFactory } from './use-cases/estate/estate-timeline/get-estate-timeline-bucket.use-case';
import { getEstateTimelineTodoUseCaseFactory } from './use-cases/estate/estate-timeline/get-estate-timeline-todo.use-case';
import { getEstateTimelineUseCaseFactory } from './use-cases/estate/estate-timeline/get-estate-timeline.use-case';
import { getEstateBuyersUseCaseFactory } from './use-cases/estate/get-estate-buyers.use-case';
import { getEstateTimelineBuyerUseCaseFactory } from './use-cases/estate/get-estate-timeline-buyer.use-case';
import { getEstateTimelineSellerUseCaseFactory } from './use-cases/estate/get-estate-timeline-seller.use-case';
import { getEstateValuationUseCaseFactory } from './use-cases/estate/get-estate-valuation.use-case';
import { getEstateValuationsUseCaseFactory } from './use-cases/estate/get-estate-valuations.use-case';
import { getEstateUseCaseFactory } from './use-cases/estate/get-estate.use-case';
import { getEstatesValuationUseCaseFactory } from './use-cases/estate/get-estates-valuation.use-case';
import { getEstatesUseCaseFactory } from './use-cases/estate/get-estates.use-case';
import { getPremarketEstatesUseCaseFactory } from './use-cases/estate/premarket/get-premarket-estates.use-case';
import { getFavoritePremarketEstatesUseCaseFactory } from './use-cases/estate/premarket/get-premarket-favorites.use-case';
import { updateEstateUseCaseFactory } from './use-cases/estate/update-estate.use-case';
import { getExternalLeadAuditUseCaseFactoryFactory } from './use-cases/external-lead-audit/external-lead-audit.use-case';
import { deleteFeedUseCaseFactory } from './use-cases/feed/delete-feed-entry.use-case';
import { listFeedUseCaseFactory } from './use-cases/feed/list-feed.use-case';
import { getFinancialDetailsUseCaseFactory } from './use-cases/financial-details/get-financial-details.use-case';
import { updateFinancialDetailsUseCaseFactory } from './use-cases/financial-details/update-financial-details.use-case';
import { createByggstartLeadUseCaseFactory } from './use-cases/leads/create-byggstart-lead.use-case';
import { createElectricityLeadUseCaseFactory } from './use-cases/leads/create-electricity-lead.use-case';
import { createExponovaLeadUseCaseFactory } from './use-cases/leads/create-exponova-lead.use-case';
import { createSectorAlarmLeadUseCaseFactory } from './use-cases/leads/create-sector-alarm-lead.use-case';
import { createStorebrandLeadUseCaseFactory } from './use-cases/leads/create-storebrand-lead.use-case';
import { createTelenorOfferUseCaseFactory } from './use-cases/leads/create-telenor-offer.use-case';
import { createVitecLeadUseCaseFactory } from './use-cases/leads/create-vitec-lead.use-case';
import { isSteddyAvailableUseCaseFactory } from './use-cases/leads/is-steddy-available.use-case';
import { getLipscoreBrokerRatingAverageUseCaseFactory } from './use-cases/lipscore-broker-rating/get-lipscore-broker-rating-average.use-case';
import { loginBrokerUseCaseFactory } from './use-cases/login/login-broker.use-case';
import { loginUseCaseFactory } from './use-cases/login/login.use-case';
import { vippsGetInfoUseCaseFactory } from './use-cases/login/vipps-get-info.use-case';
import { vippsLoginUseCaseFactory } from './use-cases/login/vipps-login.use-case';
import {
  notifyOnErrorUseCaseFactory,
  notifyOnLeadSendingSuccessErrorUseCaseFactory,
  notifyOnOTPErrorUseCaseFactory,
  notifyOnPEPErrorUseCaseFactory,
  notifyOnSettlementErrorUseCaseFactory,
} from './use-cases/notify-on-error';
import { optionallyAuthenticatedUseCaseFactory } from './use-cases/optionally-authenticated.use-case';
import { createOtpUseCaseFactory } from './use-cases/otp/create-otp.use-case';
import { deleteOtpUseCaseFactory } from './use-cases/otp/delete-otp.use-case';
import { createOtpDocumentUseCaseFactory } from './use-cases/otp/document/create-otp-document.use-case';
import { getOtpDocumentUseCaseFactory } from './use-cases/otp/document/get-otp-document.use-case';
import { sendOtpSignSmsUseCaseFactory } from './use-cases/otp/document/send-otp-sign-sms.use-case';
import { finalizeOtpUseCaseFactory } from './use-cases/otp/finalize-otp.use-case';
import { getOtpUseCaseFactory } from './use-cases/otp/get-otp.use-case';
import { createOtpMeterUseCaseFactory } from './use-cases/otp/meter/create-otp-meter.use-case';
import { deleteOtpMeterUseCaseFactory } from './use-cases/otp/meter/delete-otp-meter.use-case';
import { getOtpMeterImageUseCaseFactory } from './use-cases/otp/meter/get-otp-meter-image.use-case';
import { updateOtpMeterUseCaseFactory } from './use-cases/otp/meter/update-otp-meter.use-case';
import { createOtpParticipantUseCaseFactory } from './use-cases/otp/participant/create-otp-participant.use-case';
import { deleteOtpParticipantUseCaseFactory } from './use-cases/otp/participant/delete-otp-participant.use-case';
import { getOtpParticipantImageUseCaseFactory } from './use-cases/otp/participant/get-otp-participant-image.use-case';
import { updateOtpParticipantUseCaseFactory } from './use-cases/otp/participant/update-otp-participant.use-case';
import { updateOtpUseCaseFactory } from './use-cases/otp/update-otp.use-case';
import { createIdfyPEPFormDocumentUseCaseFactory } from './use-cases/pep-form/create-idfy-pep-document.use-case';
import { createPEPFormUseCaseFactory } from './use-cases/pep-form/create-pep.use-case';
import { deletePEPFormUseCaseFactory } from './use-cases/pep-form/delete-pep.use-case';
import { finalizePEPFormUseCaseFactory } from './use-cases/pep-form/finalize-pep.use-case';
import { getPEPDocumentSignUrlUseCaseFactory } from './use-cases/pep-form/get-pep-document-sign-url.use-case';
import { getPEPFormParticipantUseCaseFactory } from './use-cases/pep-form/get-pep-participant.use-case';
import { getPEPFormUseCaseFactory } from './use-cases/pep-form/get-pep.use-case';
import { updatePEPFormParticipantUseCaseFactory } from './use-cases/pep-form/update-pep-participant.use-case';
import { updatePEPFormUseCaseFactory } from './use-cases/pep-form/update-pep.use-case';
import { getPopupUseCaseFactory } from './use-cases/popup/get-popup.use-case';
import { updatePopupUseCaseFactory } from './use-cases/popup/update-popup.use-case';
import { getPriceGuessingDailyEstateUseCaseFactory } from './use-cases/price-guessing-estate/get-price-guessing-daily-estate.use-case';
import { getPriceGuessingReportUseCaseFactory } from './use-cases/price-guessing-estate/get-price-guessing-report.use-case';
import { getPriceGuessingScoreboardUseCaseFactory } from './use-cases/price-guessing-estate/get-price-guessing-scoreboard.use-case';
import { getPriceGuessingUserStatisticsUseCaseFactory } from './use-cases/price-guessing-estate/get-price-guessing-user-statistics.use-case';
import { getTotalGuessesTodayByUserIdUseCaseFactory } from './use-cases/price-guessing-estate/get-total-guesses-today-by-userId.use-case';
import { getUserIdsWhoFinishedForTodayUseCaseFactory } from './use-cases/price-guessing-estate/get-user-ids-who-finished-guessing-for-today.use-case';
import { submitNewGuessForPriceGuessingEstateUseCaseFactory } from './use-cases/price-guessing-estate/submit-new-guess-for-price-guessing-estate.use-case';
import { getAllRewardsUseCaseFactory } from './use-cases/referral/get-all-rewards.use-case';
import { updateRewardUseCaseFactory } from './use-cases/referral/update-reward.use-case';
import { canRegisterUseCaseFactory } from './use-cases/register/can-register.use-case';
import { registerBrokerUseCaseFactory } from './use-cases/register/register-broker.use-case';
import { registrationUseCaseFactory } from './use-cases/register/register.use-case';
import { deleteRecommendedNextActionUseCaseFactory } from './use-cases/reminder/delete-recommended-next-action.use-case';
import { getIfInsuranceOfferUseCaseFactory } from './use-cases/services/get-if-insurance-offer.use-case';
import { getOfferUseCaseFactory } from './use-cases/services/get-offer.use-case';
import { getSmsVerificationUseCaseFactory } from './use-cases/services/get-smsverification.use-case';
import { getSsgOfferUseCaseFactory } from './use-cases/services/ssg-offer.use-case';
import { getSteddyOfferUseCaseFactory } from './use-cases/services/steddy-offer.use-case';
import { getStorebrandOfferUseCaseFactory } from './use-cases/services/storebrand-offer.use-case';
import { getTryggOfferUseCaseFactory } from './use-cases/services/trygg-offer.use-case';
import { createSettlementBuyerUseCaseFactory } from './use-cases/settlement-buyer/create-settlement-buyer.use-case';
import { deleteSettlementBuyerUseCaseFactory } from './use-cases/settlement-buyer/delete-settlement-buyer.use-case';
import { createSettlementBuyerDocumentUseCaseFactory } from './use-cases/settlement-buyer/document/create-settlement-buyer-document.use-case';
import { getSettlementBuyerDocumentSignStatusUseCaseFactory } from './use-cases/settlement-buyer/document/get-settlement-buyer-document-sign-status.use-case';
import { sendSettlementBuyerSignSmsUseCaseFactory } from './use-cases/settlement-buyer/document/send-settlement-buyer-sign-sms.use-case';
import { finalizeSettlementBuyerUseCaseFactory } from './use-cases/settlement-buyer/finalize-settlement-buyer.use-case';
import { getSettlementBuyerUseCaseFactory } from './use-cases/settlement-buyer/get-settlement-buyer.use-case';
import { updateSettlementBuyerParticipantUseCaseFactory } from './use-cases/settlement-buyer/participant/update-settlement-buyer-particpant.use-case';
import { updateSettlementBuyerUseCaseFactory } from './use-cases/settlement-buyer/update-settlement-buyer.use-case';
import { updateSettlementSellerAccountsUseCaseFactory } from './use-cases/settlement-seller/account/update-settlement-seller-accounts.use-case';
import { createSettlementSellerUseCaseFactory } from './use-cases/settlement-seller/create-settlement-seller.use-case';
import { deleteSettlementSellerUseCaseFactory } from './use-cases/settlement-seller/delete-settlement-seller.use-case';
import { createSettlementSellerDocumentUseCaseFactory } from './use-cases/settlement-seller/document/create-settlement-seller-document.use-case';
import { getSettlementSellerDocumentSignStatusUseCaseFactory } from './use-cases/settlement-seller/document/get-settlement-seller-document-sign-status.use-case';
import { sendSettlementSellerSignSmsUseCaseFactory } from './use-cases/settlement-seller/document/send-settlement-seller-sign-sms.use-case';
import { finalizeSettlementSellerUseCaseFactory } from './use-cases/settlement-seller/finalize-settlement-seller.use-case';
import { getSettlementSellerUseCaseFactory } from './use-cases/settlement-seller/get-settlement-seller.use-case';
import { updateSettlementSellerLoansUseCaseFactory } from './use-cases/settlement-seller/loan/update-settlement-seller-loans.use-case';
import { updateSettlementSellerUseCaseFactory } from './use-cases/settlement-seller/update-settlement-seller.use-case';
import { statusUseCaseFactory } from './use-cases/status_use-cases/status.use-case';
import { getTimelineBucketUseCaseFactory } from './use-cases/timeline/get-timeline-bucket.use-case';
import { getTimelineUseCaseFactory } from './use-cases/timeline/get-timeline.use-case';
import { getBrokerStatsUseCaseFactory } from './use-cases/toplist/get-toplist.use-case';
import { transactedUseCaseFactory } from './use-cases/transacted.use-case';
import { getShortUrlUseCaseFactory } from './use-cases/url/get-shortened-url.use-case';
import { shortenUrlUseCaseFactory } from './use-cases/url/shorten-url.use-case';
import { changeUserPasswordUseCaseFactory } from './use-cases/user_use-cases/change-user-password.use-case';
import { deleteUserAndroidTokenUseCaseFactory } from './use-cases/user_use-cases/delete-user-android-token.use-case';
import { deleteUserIosTokenUseCaseFactory } from './use-cases/user_use-cases/delete-user-ios-token.use-case';
import { deleteUserUseCaseFactory } from './use-cases/user_use-cases/delete-user.use-case';
import { getUserUseCaseFactory } from './use-cases/user_use-cases/get-user.use-case';
import { createGoogleAnalyticsEventUseCaseFactory } from './use-cases/user_use-cases/google-analytics/create-google-analytics-event.use-case';
import { resetUserPasswordUseCaseFactory } from './use-cases/user_use-cases/reset-user-password.use-case';
import { setUserAndroidTokenUseCaseFactory } from './use-cases/user_use-cases/set-user-android-token.use-case';
import { setUserIosTokenUseCaseFactory } from './use-cases/user_use-cases/set-user-ios-token.use-case';
import { updateUserEmailUseCaseFactory } from './use-cases/user_use-cases/update-email.use-case';
import { updateUserPasswordUseCaseFactory } from './use-cases/user_use-cases/update-user-password.use-case';
import { updateUserUseCaseFactory } from './use-cases/user_use-cases/update-user.use-case';
import { LightAxiosError, toRequestData, toResponseData } from './utils/axios-logger.utils';
import { createPepParticipantsForPepSellerFormsFactory } from './utils/buyer-seller-proxy.utils';

export const create = async ({
  config,
  logger,
  vitecService,
  statisticsService,
  idfyService,
  valuationService,
  idfyClient,
  unleash,
}: {
  config: ConfigObject;
  logger: Logger;
  vitecService: VitecService;
  statisticsService: StatisticsService;
  idfyService: IdfyService;
  valuationService: ValuationService;
  idfyClient: IdfyClient;
  unleash: Unleash;
}): Promise<FastifyInstance> => {
  axios.interceptors.request.use(
    (request) => {
      const requestData = toRequestData(request);
      if (unleash.isEnabled('http-client-logging', {})) {
        logger.debug({ request: requestData });
      }
      return request;
    },
    async (error: Error) => {
      logger.error(error, 'Error during request interception');
      throw error;
    },
  );

  axios.interceptors.response.use(
    (response) => {
      const responseData = toResponseData(response);
      if (unleash.isEnabled('http-client-logging', {})) {
        logger.debug({ response: responseData });
      }
      return response;
    },

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async (error: Error & { config: AxiosRequestConfig<any>; response: AxiosResponse<any> }) => {
      const axiosError = new LightAxiosError(
        error.message,
        toRequestData(error.config),
        toResponseData(error.response),
      );
      logger.error(axiosError);
      throw axiosError;
    },
  );

  const s3 = new aws.S3({
    accessKeyId: config.s3Config.accessKeyID,
    secretAccessKey: config.s3Config.secret,
  });
  const lambda = new aws.Lambda({
    accessKeyId: config.aws.accessKeyId,
    secretAccessKey: config.aws.secretAccessKey,
    region: config.aws.region,
  });

  const slackService = slackServiceFactory({ ...config.slack });

  const sequelize = sequelizeConnection(config.pg.logging);
  const mockEndpoints = JSON.parse(config.mockEndpoints) as string[];

  const transacted = <TInput, TOutput>(useCase: AsyncUseCase<TInput, TOutput>): AsyncUseCase<TInput, TOutput> =>
    transactedUseCaseFactory({ sequelize, useCase });

  const auth = <TInput, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<WithToken<WithoutIdentity<TInput>>, TOutput> =>
    authenticatedUseCaseFactory({ authenticationService, useCase });

  const optionalAuth = <TInput, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<WithToken<WithoutIdentity<TInput>>, TOutput> =>
    optionallyAuthenticatedUseCaseFactory({ authenticationService, useCase });

  const apikeyAuth = <TInput, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<WithApiKey<TInput>, TOutput> =>
    apikeyAuthenticatedUseCaseFactory({ apiKey: config.backendApiKey, useCase });

  const notifyOnOtpFailure = <TInput extends { estateId: string }, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<TInput, TOutput> =>
    notifyOnOTPErrorUseCaseFactory({
      useCase,
      sendMessage: slackService.sendOTPError,
    });

  const notifyOnOtpFinalizeFailure = <TInput extends { estateId: string }, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<TInput, TOutput> =>
    notifyOnOTPErrorUseCaseFactory({
      useCase,
      sendMessage: slackService.sendOTPError,
      ignoreErrors: [BadRequest, UnprocessableEntity, Unauthorized],
    });

  const notifyOnSettlementBuyerFailure = <TInput extends { estateId: string }, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<TInput, TOutput> =>
    notifyOnSettlementErrorUseCaseFactory({
      useCase,
      sendMessage: async ({ error, estateId }) =>
        slackService.sendSettlementError({ error, estateId, sellerOrBuyer: SellerOrBuyer.BUYER }),
    });

  const notifyOnSettlementBuyerFinalizeFailure = <TInput extends { estateId: string }, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<TInput, TOutput> =>
    notifyOnSettlementErrorUseCaseFactory({
      useCase,
      sendMessage: async ({ error, estateId }) =>
        slackService.sendSettlementError({ error, estateId, sellerOrBuyer: SellerOrBuyer.BUYER }),
      ignoreErrors: [BadRequest, UnprocessableEntity, Unauthorized],
    });

  const notifyOnSettlementSellerFailure = <TInput extends { estateId: string }, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<TInput, TOutput> =>
    notifyOnSettlementErrorUseCaseFactory({
      useCase,
      sendMessage: async ({ error, estateId }) =>
        slackService.sendSettlementError({ error, estateId, sellerOrBuyer: SellerOrBuyer.SELLER }),
    });

  const notifyOnSettlementSellerFinalizeFailure = <TInput extends { estateId: string }, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<TInput, TOutput> =>
    notifyOnSettlementErrorUseCaseFactory({
      useCase,
      sendMessage: async ({ error, estateId }) =>
        slackService.sendSettlementError({ error, estateId, sellerOrBuyer: SellerOrBuyer.SELLER }),
      ignoreErrors: [BadRequest, UnprocessableEntity, Unauthorized],
    });

  const notifyOnLeadSendingSuccessOrFailure = <TInput, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<WithOptionalIdentity<WithLeadSendingParams<TInput>>, TOutput> =>
    notifyOnLeadSendingSuccessErrorUseCaseFactory({
      useCase,
      sendErrorMessage: async (params) => slackService.sendLeadError(params),
      sendSuccessMessage: async (params) => slackService.sendLeadSuccess(params),
      userRepository,
    });

  const notifyOnRegistrationFailure = <TInput extends { email: string }, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
    registeredWith: RegisteredWith,
  ): AsyncUseCase<TInput, TOutput> =>
    notifyOnErrorUseCaseFactory({
      useCase,
      sendMessage: slackService.sendRegistrationError,
      registeredWith,
    });

  const notifyOnPEPFailure = <TInput extends { estateVitecId: string }, TOutput>(
    useCase: AsyncUseCase<TInput, TOutput>,
  ): AsyncUseCase<TInput, TOutput> =>
    notifyOnPEPErrorUseCaseFactory({
      useCase,
      sendMessage: async ({ error, estateVitecId }) => slackService.sendPEPError({ error, estateVitecId }),
    });

  // 1 REPOSITORIES
  userModelInit({ sequelize });
  brokerModelInit({ sequelize });
  userOptionsModelInit({ sequelize });
  estateModelInit({ sequelize });
  estateChecklistModelInit({ sequelize });
  estateEventModelInit({ sequelize });
  estateEventConnectionModelInit({ sequelize });
  estateTodoModelInit({ sequelize });
  estateChecklistConnectionModelInit({ sequelize });
  dismissedActionsModelInit({ sequelize });
  deleteUserModelInit({ sequelize });
  areaModelInit({ sequelize });
  favoriteModelInit({ sequelize });
  storebrandAuditModelInit({ sequelize });
  storebrandAuditModelInitTamo({ sequelize });
  userAreaModelInit({ sequelize });
  vitecEstateExtensionsModelInit({ sequelize });
  popupModelInit({ sequelize });
  userActivityModelInit({ sequelize });
  brokerActivityModelInit({ sequelize });
  referralCodesModelInit({ sequelize });
  referralInvitesModelInit({ sequelize });
  leadAuditModelInit({ sequelize });
  referralRewardsModelInit({ sequelize });
  rewardTypesModelInit({ sequelize });
  eiendomsverdiAuditModelInit({ sequelize });
  couponModelInit({ sequelize });
  smsAuditModelInit({ sequelize });
  mailAuditModelInit({ sequelize });
  overtakeProtocolModelInit({ sequelize });
  overtakeProtocolParticipantModelInit({ sequelize });
  overtakeProtocolMeterModelInit({ sequelize });
  feedModelInit({ sequelize });
  userFirebaseTokenModelInit({ sequelize });
  urlModelInit({ sequelize });
  settlementSellerModelInit({ sequelize });
  settlementSellerLoanModelInit({ sequelize });
  settlementSellerParticipantModelInit({ sequelize });
  settlementBuyerModelInit({ sequelize });
  settlementBuyerLoanModelInit({ sequelize });
  settlementBuyerParticipantModelInit({ sequelize });
  eiendomsverdiEstateCacheModelInit({ sequelize });
  pepFormModelInit({ sequelize });
  pepFormParticipantModelInit({ sequelize });
  fileModelInit({ sequelize });
  settlementSellerAccountModelInit({ sequelize });
  settlementBuyerEquityModelInit({ sequelize });
  estateImageFromUserModelInit({ sequelize });
  estatePriceHistoryModelInit({ sequelize });
  externalLeadAuditModelInit({ sequelize });
  priceGuessingEstateModelInit({ sequelize });
  priceGuessingUserGuessModelInit({ sequelize });

  setUserModelReferences();
  setUserOptionsModelReferences();
  setUserActivityModelReferences();
  setBrokerActivityModelReferences();
  setEstateModelReferences();
  setEstateChecklistModelReferences();
  setEstateTodoModelReferences();
  setEstateChecklistConnectionModelReferences();
  setAreaModelReferences();
  setUserAreaModelReferences();
  setPopupModelReferences();
  setReferralCodesModelReferences();
  setReferralInvitesModelReferences();
  setRewardTypesModelReferences();
  setReferralRewardsModelReferences();
  setCouponModelReferences();
  setEiendomsverdiModelReferences();
  setFavoriteModelReferences();
  setLeadAuditModelReferences();
  setOvertakeProtocolParticipantModelReferences();
  setOvertakeProtocolMeterModelReferences();
  setEstateEventConnectionModelReferences();
  setFeedModelReferences();
  setUserFirebaseTokenModelReferences();
  setSettlementBuyerModelReferences();
  setSettlementSellerModelReferences();
  setSettlementBuyerParticipantModelReferences();
  setPEPFormModelReferences();
  setPriceGuessingEstateModelReferences();

  const mongooseConnection = mongooseConnectionFactory({
    connectionString: config.mongo.connection,
    db: config.mongo.db,
    logger,
  });
  const passwordHelper = passwordHelperFactory({ saltRounds: config.saltRounds }); // todo: repos shouldnt use password helper
  const estateRepository = estateRepositoryFactory();
  const estateMongooseRepository = estateMongooseRepositoryFactory({ connection: mongooseConnection });
  const dismissActionRepository = dismissedActionsRepositoryFactory();
  const employeeMongooseRepository = employeeMongooseRepositoryFactory({ connection: mongooseConnection });
  const vitecEstateExtensionRepository = vitecEstateExtensionRepositoryFactory();
  const areaRepository = areaRepositoryFactory();
  const popupRepository = popupRepositoryFactory();
  const userOptionsRepository = userOptionsRepositoryFactory();
  const userActivityRepository = userActivityRepositoryFactory();
  const brokerActivityRepository = brokerActivityRepositoryFactory();
  const referralRepository = referralRepositoryFactory();
  const leadAuditRepository = leadAuditRepositoryFactory();
  const userRepository = userRepositoryFactory({ passwordHelper });
  const brokerRepository = brokerRepositoryFactory({ passwordHelper });
  const favoritesRepository = favoriteRepositoryFactory();
  const estateChecklistRepository = estateChecklistRepositoryFactory();
  const estateEventRepository = estateEventRepositoryFactory();
  const estateTodoRepository = estateTodoRepositoryFactory();
  const estateChecklistConnectionRepository = estateChecklistConnectionRepositoryFactory();
  const estateEventConnectionRepository = estateEventConnectionRepositoryFactory();
  const userAreaRepository = userAreaRepositoryFactory();
  const storebrandAuditRepository = storebrandAuditRepositoryFactory();
  const storebrandVitecAuditRepository = storebrandVitecAuditRepositoryFactory();
  const buyerMongooseRepository = buyerMongooseRepositoryFactory({
    connection: mongooseConnection,
  });
  const sellerMongooseRepository = sellerMongooseRepositoryFactory({
    connection: mongooseConnection,
  });
  const contactRepository = contactMongooseRepositoryFactory({ connection: mongooseConnection });
  const departmentRepository = departmentMongooseRepositoryFactory({ connection: mongooseConnection });
  const referralRewardRepository = referralRewardRepositoryFactory();
  const smsAuditRepository = smsAuditRepositoryFactory();
  const mailAuditRepository = mailAuditRepositoryFactory();
  const otpRepository = overtakeProtocolRepositoryFactory();
  const feedRepository = feedRepositoryFactory();
  const userFirebaseTokenRepository = userFirebaseTokenRepositoryFactory();
  const urlRepository = urlRepositoryFactory();
  const fileRepo = fileRepoFactory();
  const settlementSellerRepository = settlementSellerRepositoryFactory();
  const settlementBuyerRepository = settlementBuyerRepositoryFactory();
  const pepFormRepository = pepFormRepositoryFactory();
  const pepFormParticipantRepository = pepFormParticipantRepositoryFactory();
  const otpMeterRepo = otpMeterRepoFactory();
  const otpParticipantRepo = otpParticipantRepoFactory();
  const lipscoreBrokerRatingRepository = lipscoreBrokerRatingRepositoryFactory({ config, sequelize });
  const estateImageFromUserRepository = estateImageFromUserRepositoryFactory();
  const estatePriceHistoryRepository = estatePriceHistoriesRepositoryFactory();
  const externalLeadAuditRepository = ExternalLeadAuditRepositoryFactory();
  const departmentMongooseRepository = departmentMongooseRepositoryFactory({ connection: mongooseConnection });
  const settlementBuyerParticipantRepository = settlementBuyerParticipantRepositoryFactory();
  const priceGuessingEstateRepository = priceGuessingEstateRepositoryFactory();
  const priceGuessingUserGuessRepository = priceGuessingUserGuessRepositoryFactory({ sequelize });
  const proxyMongooseRepository = proxyMongooseRepositoryFactory({ connection: mongooseConnection });

  // 2 SERVICES
  const estateTimelineSellerService = estateTimelineSellerServiceFactory();
  const estateTimelineBuyerService = estateTimelineBuyerServiceFactory();
  const popupService = popupServiceFactory(popupRepository);
  const mapboxService = mapboxServiceFactory(
    config.mapbox.accessToken,
    config.mapbox.baseUrl,
    config.mapbox.minRelevance,
  );
  const timelineService = estateTimelineSellerServiceFactory();
  const wealthManagementService = wealthManagementServiceFactory();
  const brokerService = brokerServiceFactory({ employeeMongooseRepository });
  const awsS3Service = awsS3ServiceFactory({ s3 });
  const estateService = estateServiceFactory({
    config: {
      mapboxConfig: config.mapbox,
      awsS3Config: config.s3Config,
    },
    estateRepository,
    estateMongooseRepository,
    areaRepository,
    valuationService,
    vitecEstateExtensionRepository,
    mapboxService,
    wealthManagementService,
    timelineService,
    awsS3Service,
    storebrandAuditRepository,
    unleash,
  });
  const tokenService = jwtTokenServiceFactory({
    secret: config.jwt.secret,
    expiresIn: config.jwt.expiresIn.tokenService,
    iss: config.jwt.issuer,
  });
  const resetPasswordTokenService = (jwtTokenServiceFactory({
    secret: config.jwt.secret,
    expiresIn: config.jwt.expiresIn.resetPasswordTokenService,
    iss: config.jwt.issuer,
  }) as unknown) as TokenService<string, { email: string; userID: string }>;
  const emailVerificationTokenService = (jwtTokenServiceFactory({
    secret: config.jwt.secret,
    expiresIn: config.jwt.expiresIn.emailVerificationTokenService,
    iss: config.jwt.issuer,
  }) as unknown) as TokenService<string, { brokerId: string }>;
  const mailService = mailServiceFactory({
    mailAuditRepository,
    apikey: config.mail.sendgridApikey,
    senderAddress: config.mail.senderAddress,
    silentFail: config.mail.silentFail,
  });
  const smsService = smsServiceFactory({
    smsAuditRepository,
    twilioAccountSid: config.twilo.accountSid,
    twilioAuthToken: config.twilo.authToken,
    twilioServiceName: config.twilo.serviceName,
    twilioVerificationDisabled: config.twilo.disabled,
    twilioFromNumber: config.twilo.fromNumber,
    twilioDisableTextMessages: config.twilo.disableTextMessages,
    unleash,
    vitecService,
  });
  const authenticationService = authenticationServiceFactory({
    tokenService,
    userRepository,
    brokerRepository,
  });
  const leadService = await leadsServiceFactory({
    rootDir: config.rootFolder,
    leadAuditRepository,
    estateMongooseRepository,
    departmentMongooseRepository,
  });
  const brokerEmailVerificationService = brokerEmailVerificationServiceFactory({
    mailService,
    brokerRepository,
    brokerService,
    tokenService: emailVerificationTokenService,
    mail: { domain: config.frontendDomain, sender: config.mail.senderAddress },
  });
  const estateChecklistService = estateChecklistServiceFactory({
    estateTodoRepository,
    estateChecklistRepository,
    estateChecklistConnectionRepository,
  });

  const estateEventService = estateEventServiceFactory({
    estateEventRepository,
    estateEventConnectionRepository,
  });
  const estateTimelineService = estateTimelineServiceFactory({ estateChecklistService, estateEventService });

  const storebrandService = storebrandServiceFactory({
    config: config.storebrand,
    storebrandAuditRepository,
    userActivityRepository,
    externalLeadAuditRepository,
    leadService,
  });

  const storebrandVitecService = storebrandVitecServiceFactory({
    vitecService,
    storebrandAuditRepository: storebrandVitecAuditRepository,
    leadService,
    externalLeadAuditRepository,
    userActivityRepository,
    settlementBuyerRepository,
    settlementSellerRepository,
    estateRepository: estateMongooseRepository,
  });

  const evRestApiClient = createEiendomsverdiRestApiClient({
    axios,
    tokenUrl: config.eiendomsverdi.tokenUrlRest,
    clientId: config.eiendomsverdi.clientIdRest,
    clientSecret: config.eiendomsverdi.clientSecretRest,
    apiUrl: config.eiendomsverdi.apiUrlRest,
    estimateApiUrl: config.eiendomsverdi.estimateApiUrlRest,
  });

  const telenorService = telenorServiceFactory({
    config: config.telenor,
    externalLeadAuditRepository,
    leadService,
    evApi: evRestApiClient,
    estateRepository: estateMongooseRepository,
    settlementBuyerRepository: settlementBuyerRepository,
    vitecService,
  });

  const steddyService = steddyServiceFactory({
    config: config.steddy,
    externalLeadAuditRepository,
    leadService,
    estateRepository: estateMongooseRepository,
    settlementBuyerRepository: settlementBuyerRepository,
    userActivityRepository: userActivityRepository,
  });

  const secretsManagerService = secretsManagerServiceFactory({
    config: config.aws,
  });
  if (process.env.NODE_ENV === 'production') {
    config.statistics.nordvikboligNo.googleAnalytics.privateKey =
      (await secretsManagerService.getSecretByKey('GOOGLE_ANALYTICS_PRIVATE_KEY')) || '';

    config.storebrand.privateKey = (await secretsManagerService.getSecretByKey('STOREBRAND_PRIVATE_KEY')) || '';
  }
  const customerService = customerServiceFactoryForBrokers({
    buyerMongooseRepository,
    sellerMongooseRepository,
  });
  const referralService = await createReferralService({
    referralRepository,
    referralRewardRepository,
    popupService,
    userRepository,
    mailService,
  });

  const fileService = fileServiceFactory({ fileRepo, awsS3Service });

  const electricityLeadService = createElectricityLeadService({
    externalLeadAuditRepository,
    leadService,
    norgesConfig: config.norges,
    fortumConfig: config.fortum,
    fjordkraftConfig: config.fjordkraft,
    trondelagSpotConfig: config.trondelagSpot,
    trondelagTobbSpotConfig: config.trondelagTobbSpot,
  });

  const vippsService = vippsServiceFactory({
    ...config.vipps,
    adultYear: config.adultYear,
  });

  const estateSyncService = estateSyncServiceFactory({
    estateService,
    valuationService,
  });

  const settlementSellerDocumentService = settlementSellerDocumentServiceFactory({
    generatePdf: async (htmlString) =>
      htmlToPdf(htmlString, unleash, lambda, {
        format: 'a4',
        margin: { bottom: '15mm', left: '15mm', right: '15mm', top: '15mm' },
      }),
  });
  const settlementBuyerDocumentService = settlementBuyerDocumentServiceFactory({
    generatePdf: async (htmlString) =>
      htmlToPdf(htmlString, unleash, lambda, {
        format: 'a4',
        margin: { bottom: '15mm', left: '15mm', right: '15mm', top: '15mm' },
      }),
  });
  const overtakeProtocolDocumentService = overtakeProtocolDocumentServiceFactory({
    generatePdf: async (htmlString) =>
      htmlToPdf(htmlString, unleash, lambda, {
        format: 'a4',
        margin: { bottom: '15mm', left: '15mm', right: '15mm', top: '15mm' },
      }),
  });
  const overtakeOfEmptyEstateDocumentService = overtakeProtocolOfEmptyEstateDocumentServiceFactory({
    generatePdf: async (htmlString) =>
      htmlToPdf(htmlString, unleash, lambda, {
        format: 'a4',
        margin: { bottom: '15mm', left: '15mm', right: '15mm', top: '15mm' },
      }),
  });
  const politicallyExposedPersonDocumentService = politicallyExposedPersonDocumentServiceFactory({
    generatePdf: async (htmlString) =>
      htmlToPdf(htmlString, unleash, lambda, {
        format: 'a4',
        margin: { bottom: '15mm', left: '15mm', right: '15mm', top: '15mm' },
      }),
  });

  const otpService = otpServiceFactory({
    estateService,
    otpRepository,
    fileRepo,
    fileService,
    overtakeProtocolDocumentService,
    overtakeOfEmptyEstateDocumentService,
    otpMeterRepo,
    otpParticipantRepo,
  });

  const sectorAlarmService = sectorAlarmServiceFactory({
    config: config.sectorAlarm,
    userActivityRepository,
    externalLeadAuditRepository,
    leadService,
  });

  const kokkelorenService = kokkelorenServiceFactory({
    config: config.kokkeloren,
    estateRepository: estateMongooseRepository,
    settlementBuyerRepository: settlementBuyerRepository,
    userActivityRepository,
    externalLeadAuditRepository,
    leadService,
  });

  const serviceOfferService = serviceOfferServiceFactory({
    leadService,
    mailService,
    serviceOfferEmailReceiverAddress: {
      [ServiceOfferViaEmailType.HMH_CLEANING]: config.mail.serviceOffer.hmh,
      [ServiceOfferViaEmailType.HMH_MOVING]: config.mail.serviceOffer.hmh,
      [ServiceOfferViaEmailType.OSLO_TRANSPORT]: config.mail.serviceOffer.osloTransport,
      [ServiceOfferViaEmailType.IF_INSURANCE]: config.mail.serviceOffer.if,
      [ServiceOfferViaEmailType.TRYGG]: config.mail.serviceOffer.trygg,
      [ServiceOfferViaEmailType.SSG]: config.mail.serviceOffer.ssg,
      [ServiceOfferViaEmailType.EXPO_NOVA]: config.exponovaEmail,
      [ServiceOfferViaEmailType.CHILL]: config.mail.serviceOffer.chill,
      [ServiceOfferViaEmailType.VERKET_INTERIOR]: config.mail.serviceOffer.verketInterior,
      [ServiceOfferViaEmailType.RENTAL]: config.mail.serviceOffer.rental,
    },
    userActivityRepository,
    userRepository,
    storebrandService,
    storebrandVitecService,
    externalLeadAuditRepository,
    sectorAlarmService,
    telenorService,
    steddyService,
    kokkelorenService,
    estateService,
  });

  const settlementService = settlementServiceFactory({
    serviceOfferService,
    externalLeadAuditRepository,
    idfyService,
    mailService,
  });
  const lipscoreBrokerRatingService = lipscoreBrokerRatingServiceFactory({ lipscoreBrokerRatingRepository });

  const createPepParticipantsWithSellersAndProxies = createPepParticipantsForPepSellerFormsFactory({
    pepParticipantRepo: pepFormParticipantRepository,
    proxyMongooseRepository,
    sellerMongooseRepository,
  });

  // 3 USE-CASES
  const statusUseCase = statusUseCaseFactory();
  const createVitecLeadUseCase = pipe(
    notifyOnLeadSendingSuccessOrFailure,
    auth,
    transacted,
  )(
    createVitecLeadUseCaseFactory({
      leadsUrl: config.oldNordikApiUrl,
      sendLeads: config.sendVitecLeads,
      leadService,
      userActivityRepository,
      estateMongooseRepository,
    }),
  );
  const regUseCase = transacted(
    registrationUseCaseFactory({
      userRepository,
      userActivityRepository,
      userOptionsRepository,
      tokenService,
      estateService,
      mailService,
      slackService,
    }),
  );
  const getSmsVerificationUseCase = getSmsVerificationUseCaseFactory(smsService);
  const regBrokerUsecase = transacted(
    registerBrokerUseCaseFactory({
      brokerRepository,
      brokerActivityRepository,
      brokerService,
      brokerEmailVerificationService,
    }),
  );

  const verifyBrokerEmailUseCase = verifyBrokerEmailUseCaseFactory({
    brokerRepository,
    tokenService: emailVerificationTokenService,
  });

  const bankIdIframeUseCase = bankIdIframeUseCaseFactory({
    idfyService,
  });

  const bankIdSessionUseCase = bankIdSessionUseCaseFactory({
    idfyService,
    logger,
  });

  const loginBrokerUseCase = loginBrokerUseCaseFactory(
    brokerRepository,
    brokerActivityRepository,
    tokenService,
    passwordHelper,
    brokerEmailVerificationService,
  );
  const getEstateTimelineSellerUseCase = auth(
    getEstateTimelineSellerUseCaseFactory({
      estateService,
      timelineService: estateTimelineSellerService,
    }),
  );
  const getEstateTimelineBuyerUseCase = auth(
    getEstateTimelineBuyerUseCaseFactory({
      estateService,
      timelineService: estateTimelineBuyerService,
    }),
  );
  const getTimelineUseCase = getTimelineUseCaseFactory({
    estateTimelineService,
  });
  const getEstateTimelineUseCase = getEstateTimelineUseCaseFactory({
    estateTimelineService,
    estateService,
  });
  const getTimelineBucketUseCase = getTimelineBucketUseCaseFactory({
    estateTimelineService,
  });
  const getEstateTimelineBucketUseCase = getEstateTimelineBucketUseCaseFactory({
    estateTimelineService,
    estateService,
  });
  const getEstateTodoUseCase = getEstateTimelineTodoUseCaseFactory({
    estateTimelineService,
    estateService,
  });
  const getEstateValuationUseCase = getEstateValuationUseCaseFactory({
    estateService,
    userRepository,
  });
  const getEstatesValuationUseCase = getEstatesValuationUseCaseFactory({
    estateService,
    userRepository,
  });
  const getAreasUseCase = getAreasUseCaseFactory({
    areaRepository,
  });
  const setUserIosTokenUseCase = setUserIosTokenUseCaseFactory({
    userFirebaseTokenRepository,
  });
  const deleteUserIosTokenUseCase = deleteUserIosTokenUseCaseFactory({
    userFirebaseTokenRepository,
  });
  const setUserAndroidTokenUseCase = setUserAndroidTokenUseCaseFactory({
    userFirebaseTokenRepository,
  });
  const deleteUserAndroidTokenUseCase = deleteUserAndroidTokenUseCaseFactory({
    userFirebaseTokenRepository,
  });

  const updateSettlementBuyerParticipantUseCase = updateSettlementBuyerParticipantUseCaseFactory({
    settlementBuyerParticipantRepository,
  });

  const createPepFormUseCase = createPEPFormUseCaseFactory({
    pepFormRepository,
    estateService,
    createPepParticipantsWithSellersAndProxies,
  });

  // 4 ENDPOINTS
  const statusEndpoint = statusEndpointFactory();

  // Admin endpoints
  const adminAuthEndpoint = adminAuthEndpointFactory({
    adminApiKey: config.backendApiKey,
    rateLimit: config.admin.rateLimit,
  });
  const adminInterfaceEndpoint = adminInterfaceEndpointFactory();
  const searchUsersEndpoint = searchUsersEndpointFactory({
    useCase: apikeyAuth(searchUsersUseCaseFactory(userRepository)),
    rateLimit: config.admin.rateLimit,
  });
  const impersonateUserEndpoint = impersonateUserEndpointFactory({
    useCase: apikeyAuth(impersonateUserUseCaseFactory(userRepository, tokenService)),
    rateLimit: config.admin.rateLimit,
  });

  const createVitecLeadEndpoint = createVitecLeadEndpointFactory({
    createVitecLeadUseCase,
    rateLimit: config.services.rateLimit,
  });
  const loginEndpoint = loginEndpointFactory({
    loginUseCase: pipe(transacted)(
      loginUseCaseFactory(userRepository, userActivityRepository, tokenService, passwordHelper, popupService),
    ),
    rateLimit: config.login.rateLimit,
  });
  const loginBrokerEndpoint = brokerLoginEndpointFactory({ loginBrokerUseCase });
  const registerEndpoint = registerEndpointFactory({
    registerUseCase: notifyOnRegistrationFailure(regUseCase, RegisteredWith.PHONE_NUMBER),
  });
  const getSmsVerificationEndpoint = getSmsVerificationEndpointFactory({ useCase: getSmsVerificationUseCase });
  const verifySmsCodeEndpoint = verifySmsCodeEndpointFactory({ useCase: getSmsVerificationUseCase });
  const registerBrokerEndpoint = registerBrokerEndpointFactory({ registerBrokerUseCase: regBrokerUsecase });
  const verifyBrokerEmailEndpoint = verifyBrokerEmailEndpointFactory(verifyBrokerEmailUseCase);
  //TODO: BankID Reg endpoint
  const registerBankIdEndpoint = registerBankIdEndpointFactory({
    adultYear: config.adultYear,
    bankIdSessionUseCase,
    registerUseCase: notifyOnRegistrationFailure(regUseCase, RegisteredWith.BANKID),
  });
  const bankIdIframeEndpoint = bankIdIframeEndpointFactory(bankIdIframeUseCase, logger);
  const bankIdSessionEndpoint = bankIdSessionEndpointFactory(bankIdSessionUseCase);
  const authenticatedStatusEndpoint = authenticatedStatusEndpointFactory({ useCase: auth(statusUseCase) });
  const getUserEndpoint = getUserEndpointFactory({
    useCase: pipe(auth, transacted)(getUserUseCaseFactory({ userRepository, employeeMongooseRepository })),
  });
  const updateUserEndpoint = updateUserEndpointFactory({
    useCase: pipe(
      auth,
      transacted,
    )(
      updateUserUseCaseFactory({
        userRepository,
        userOptionsRepository,
        userAreaRepository,
        userActivityRepository,
      }),
    ),
  });
  const getEstatesEndpoint = getEstatesEndpointFactory({
    getEstatesUseCase: pipe(auth)(
      getEstatesUseCaseFactory({
        userRepository,
        estateService,
        userActivityRepository,
      }),
    ),
  });
  const getEstateEndpoint = getEstateEndpointFactory({
    getEstateUseCase: pipe(auth)(
      getEstateUseCaseFactory({
        estateService,
        userOptionsRepository,
        valuationService,
        userActivityRepository,
        estateMongooseRepository,
      }),
    ),
  });
  const createEstateEndpoint = createEstateEndpointFactory({
    createEstateUseCase: pipe(
      auth,
      transacted,
    )(
      createEstateUseCaseFactory({
        estateRepository,
      }),
    ),
  });
  const getEstateTimelineSellerEndpoint = getEstateTimelineSellerEndpointFactory({
    getEstateTimelineSellerUseCase: transacted(getEstateTimelineSellerUseCase),
  });
  const getEstateTimelineBuyerEndpoint = getEstateTimelineBuyerEndpointFactory({
    getEstateTimelineBuyerUseCase: transacted(getEstateTimelineBuyerUseCase),
  });
  const getTimelineEndpoint = getTimelineEndpointFactory({
    useCase: getTimelineUseCase,
  });
  const getEstateTimelineEndpoint = getEstateTimelineEndpointFactory({
    useCase: auth(getEstateTimelineUseCase),
  });
  const getTimelineBucketEndpoint = getTimelineBucketEndpointFactory({
    useCase: getTimelineBucketUseCase,
  });
  const getEstateTimelineBucketEndpoint = getEstateTimelineBucketEndpointFactory({
    useCase: auth(getEstateTimelineBucketUseCase),
  });
  const getEstateTodoEndpoint = getEstateTimelineTodoEndpointFactory({
    useCase: auth(getEstateTodoUseCase),
  });
  const getEstateValuationEndpoint = getEstateValuationEndpointFactory({
    useCase: auth(getEstateValuationUseCase),
  });
  const getEstatesValuationEndpoint = getEstatesValuationEndpointFactory({
    useCase: auth(getEstatesValuationUseCase),
  });
  const toggleEstateChecklistTodoEndpoint = toggleEstateChecklistTodoEndpointFactory({
    toggleEstateChecklistTodoUseCase: pipe(
      auth,
      transacted,
    )(
      toggleEstateChecklistTodoUseCaseFactory({
        estateChecklistConnectionRepository,
        estateChecklistService,
      }),
    ),
  });
  const setEstateEventDateEndpoint = setEstateEventDateEndpointFactory({
    useCase: pipe(
      auth,
      transacted,
    )(
      setEstateEventDateUseCaseFactory({
        estateEventConnectionRepository,
        estateService,
        userRepository,
      }),
    ),
  });
  const getEstateChecklistsEndpoint = getEstateChecklistsEndpointFactory({
    getEstateChecklistsUseCase: auth(
      getEstateChecklistsUseCaseFactory({
        estateChecklistService,
      }),
    ),
  });
  const getSellersEndpoint = getSellersEndpointFactory({
    getSellersUseCase: auth(
      getSellersUseCaseFactory({
        estateService,
        brokerRepository,
        brokerActivityRepository,
        dismissedActionsRepository: dismissActionRepository,
      }),
    ),
  });
  const getBrokerLeadsEndpoint = getBrokerLeadsEndpointFactory({
    getBrokerLeadsUseCase: pipe(
      auth,
      transacted,
    )(
      brokerLevelUseCaseFactory({
        useCase: getBrokerLeadsUseCaseFactory({
          userRepository,
          brokerRepository,
          estateService,
          dismissedActionRepository: dismissActionRepository,
        }),
      }),
    ),
  });
  const getBuyersEndpoint = getBuyersEndpointFactory({
    getBuyersUseCase: auth(
      brokerLevelUseCaseFactory({
        useCase: getBuyersUseCaseFactory({
          estateService,
          buyerRepository: buyerMongooseRepository,
          brokerRepository,
          dismissedActionRepository: dismissActionRepository,
        }),
      }),
    ),
  });
  const getOwnersEndpoints = getOwnersEndpointFactory({
    getOwnersUseCase: auth(
      brokerLevelUseCaseFactory({
        useCase: getOwnersUseCaseFactory({
          estateService,
          brokerRepository,
          buyerRepository: buyerMongooseRepository,
          dismissedActionsRepository: dismissActionRepository,
          userRepository,
        }),
      }),
    ),
  });
  const getBrokerEndpoint = getBrokerEndpointFactory({
    getBrokerUseCase: auth(
      getBrokerUseCaseFactory({ brokerRepository, employeeRepository: employeeMongooseRepository }),
    ),
  });
  const getBrokerStats = getBrokerStatsEndpointFactory({
    getBrokerStatsUseCase: auth(
      brokerLevelUseCaseFactory({
        useCase: getBrokerStatsUseCaseFactory({
          brokerRepository,
          brokerActivityRepository,
          estateService,
          employeeRepository: employeeMongooseRepository,
          departmentRepository,
        }),
      }),
    ),
  });
  const getBrokerEstatesEndpoint = getBrokerEstatesEndpointFactory({
    getEstatesUseCase: auth(
      getBrokerEstatesUseCaseFactory({ estateService, brokerRepository, brokerActivityRepository, areaRepository }),
    ),
  });
  const getBrokerCustomerEndpoint = getBrokerCustomerEndpointFactory({
    getBrokerCustomerUseCase: pipe(
      auth,
      transacted,
    )(
      getBrokerCustomerUseCaseFactory({
        estateService,
        timelineService,
        userRepository,
        customerService,
        dismissedActionsRepository: dismissActionRepository,
      }),
    ),
  });
  const deleteRecommendedNextActionEndpoint = deleteRecommenddedNextActionEndpointFactory({
    deleteRecommendedNextActionUseCase: pipe(
      auth,
      transacted,
    )(deleteRecommendedNextActionUseCaseFactory({ dismissActionRepository })),
  });
  const estateTimelineBrokerEndpoint = getEstateTimelineBrokerEndpointFactory({
    getEstateTimelineUseCase: auth(
      getUserTimelineSellerUseCaseFactory({ estateService, timelineService: estateTimelineSellerService }),
    ),
  });
  const getBrokerEstatePotentialBuyersEndpoint = getBrokerEstatePotentialBuyersFactory({
    mockEndpoints,
    getBrokerEstatePotentialBuyersUseCase: pipe(
      auth,
      transacted,
    )(
      getBrokerEstatePotentialBuyersUseCaseFactory({
        userRepository,
        estateService,
        contactRepository,
      }),
    ),
  });
  const getSignableUserDocumentsCountEndpoint = getSignableUserDocumentCountEndpointFactory({
    getSignableUserDocumentsCountUseCase: auth(
      getSignableUserDocumentCountUseCaseFactory({
        estateMongooseRepository,
        userRepository,
        settlementBuyerRepository,
        settlementSellerRepository,
        otpRepository,
        pepFormRepository,
        unleash,
      }),
    ),
  });
  const getEstateDocumentsEndpoint = getEstateDocumentsEndpointFactory({
    getEstateDocumentsUseCase: auth(
      getCompletedEstateDocumentsUseCaseFactory({
        estateMongooseRepository,
        signUrlUseCase: getSignedUrlUseCaseFactory(),
      }),
    ),
  });
  const getSignableEstateDocumentsEndpoint = getSignableEstateDocumentsEndpointFactory({
    getSignableEstateDocumentsUseCase: auth(
      getSignableEstateDocumentsUseCaseFactory({
        estateMongooseRepository,
        settlementBuyerRepository,
        settlementSellerRepository,
        otpRepository,
        pepFormRepository,
        vitecService,
        unleash,
      }),
    ),
  });
  const getPremarketEstatesEndpoint = getPremarketEstatesEndpointFactory({
    useCase: auth(
      getPremarketEstatesUseCaseFactory({
        estateService,
        areaRepository,
        userRepository,
        favoritesRepository,
        config: config.premarket,
        userActivityRepository,
      }),
    ),
  });
  const getFavoritePremarketEstatesEndpoint = getFavoritePremarketEstatesEndpointFactory({
    useCase: auth(
      getFavoritePremarketEstatesUseCaseFactory({
        estateService,
        userRepository,
        favoritesRepository,
        config: config.premarket,
        userActivityRepository,
      }),
    ),
  });
  const downloadDocumentEndpoint = downloadDocumentEndpointFactory({
    downloadDocument: downloadDocumentUseCaseFactory({ vitecService }),
  });
  const getSignedUrlEndpoint = getSignedUrlEndpointFactory({
    getSignedUrl: auth(getSignedUrlUseCaseFactory()),
  });
  const getOfferEndpoint = getOfferEndpointFactory({
    useCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
      transacted,
    )(getOfferUseCaseFactory({ serviceOfferService })),
    rateLimitConfig: config.services.rateLimit,
  });

  const getTryggOfferEndpoint = getTryggOfferEndpointFactory({
    useCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
      transacted,
    )(getTryggOfferUseCaseFactory({ serviceOfferService })),
    rateLimitConfig: config.services.rateLimit,
  });

  const getSsgOfferEndpoint = getSsgOfferEndpointFactory({
    useCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
      transacted,
    )(getSsgOfferUseCaseFactory({ serviceOfferService })),
    rateLimitConfig: config.services.rateLimit,
  });

  const getStorebrandOfferEndpoint = getStorebrandOfferEndpointFactory({
    useCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
      transacted,
    )(
      getStorebrandOfferUseCaseFactory({
        storebrandService: storebrandVitecService,
        userRepository,
      }),
    ),
    rateLimitConfig: config.services.rateLimit,
  });

  const getSteddyOfferEndpoint = getSteddyOfferEndpointFactory({
    useCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
      transacted,
    )(getSteddyOfferUseCaseFactory({ steddyService, userRepository })),
    rateLimitConfig: config.services.rateLimit,
  });

  const resetUserPasswordEndpoint = resetUserPasswordEndpointFactory({
    useCase: transacted(
      resetUserPasswordUseCaseFactory({
        mailService,
        userRepository,
        tokenService: resetPasswordTokenService,
        mail: { domain: config.frontendDomain, sender: config.mail.senderAddress },
      }),
    ),
  });
  const updateUserPasswordEndpoint = updateUserPasswordEndpointFactory({
    useCase: transacted(
      updateUserPasswordUseCaseFactory({
        passwordHelper,
        userRepository,
        tokenService: resetPasswordTokenService,
      }),
    ),
  });
  const resetBrokerPasswordEndpoint = resetBrokerPasswordEndpointFactory({
    resetBrokerPasswordUseCase: transacted(
      resetBrokerPasswordUseCaseFactory({
        mail: {
          sender: config.mail.senderAddress,
          domain: config.frontendDomain,
        },
        mailService,
        brokerRepository,
        tokenService: resetPasswordTokenService,
      }),
    ),
  });
  const updateBrokerPasswordEndpoint = updateBrokerPasswordEndpointFactory({
    updateBrokerPasswordUseCase: transacted(
      updateBrokerPasswordUseCaseFactory({
        passwordHelper,
        brokerRepository,
        tokenService: resetPasswordTokenService,
      }),
    ),
  });
  const getEstateSalesEndpoint = getEstateSalesEndpointFactory({
    useCase: auth(
      getEstateValuationsUseCaseFactory({
        remoteEstateRepository: estateMongooseRepository,
        valuationService,
        localEstateRepository: estateRepository,
      }),
    ),
  });

  const deleteUserEndpoint = deleteUserEndpointFactory({
    useCase: pipe(
      auth,
      transacted,
    )(
      deleteUserUseCaseFactory({
        userRepository,
        mailService,
        passwordHelper,
      }),
    ),
  });
  const updateUserEmailEndpoint = updateUserEmailEndpointFactory({
    useCase: pipe(auth, transacted)(updateUserEmailUseCaseFactory({ userRepository, mailService, passwordHelper })),
  });
  const getAreasEndpoint = getAreasEndpointFactory(getAreasUseCase);
  const updateEstateEndpoint = updateEstateEndpointFactory({
    updateEstateUseCase: pipe(auth, transacted)(updateEstateUseCaseFactory({ estateRepository })),
  });
  const changeUserPasswordEndpoint = changeUserPasswordEndpointFactory({
    useCase: pipe(
      auth,
      transacted,
    )(
      changeUserPasswordUseCaseFactory({
        userRepository,
        passwordHelper,
        mailService,
      }),
    ),
  });
  const changeBrokerPasswordEndpoint = changeBrokerPasswordEndpointFactory({
    useCase: pipe(
      auth,
      transacted,
    )(
      changeBrokerPasswordUseCaseFactory({
        brokerRepository,
        passwordHelper,
        mailService,
      }),
    ),
  });
  const addFavoriteEndpoint = addFavoriteEndpointFactory({
    createFavoriteUseCase: pipe(
      auth,
      transacted,
    )(addFavoriteUseCaseFactory({ userRepository, favoritesRepository, userActivityRepository })),
  });
  const deleteFavoriteEndpoiont = deleteFavoriteEndpointFactory({
    createFavoriteUseCase: pipe(
      auth,
      transacted,
    )(deleteFavoriteUseCaseFactory({ favoritesRepository, userActivityRepository })),
  });
  const featureFlagEndpoint = getFeatureFlagEndpointFactory({ unleash });

  const createTelenorOfferEndpoint = createTelenorOfferEndpointFactory({
    createTelenorOfferUseCase: pipe(optionalAuth)(
      createTelenorOfferUseCaseFactory({
        telenorService,
      }),
    ),
    rateLimit: config.services.rateLimit,
  });

  const isSteddyAvailableEndpoint = isSteddyAvailableEndpointFactory({
    isSteddyAvailableUseCase: pipe(optionalAuth)(
      isSteddyAvailableUseCaseFactory({
        steddyService,
      }),
    ),
    rateLimit: config.services.rateLimit,
  });

  const createStorebrandLeadEndpoint = createStorebrandLeadEndpointFactory({
    createStorebrandLeadUseCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
    )(
      createStorebrandLeadUseCaseFactory({
        userRepository,
        storebrandService,
        sendLeads: config.sendVitecLeads,
        storebrandAuditRepository,
      }),
    ),
    rateLimit: config.services.rateLimit,
  });
  const getFinancialDetailsEndpoint = getFinancialDetailsEndpointFactory({
    getFinancialDetailsUseCase: auth(
      getFinancialDetailsUseCaseFactory({
        userRepository,
        estateRepository,
        estateMongooseRepository,
        vitecEstateExtensionRepository,
      }),
    ),
  });
  const updateFinancialDetailsEndpoint = updateFinancialDetailsEndpointFactory({
    updateFinancialDetailsUseCase: pipe(
      auth,
      transacted,
    )(
      updateFinancialDetailsUseCaseFactory({
        userRepository,
        estateRepository,
        estateMongooseRepository,
        vitecEstateExtensionRepository,
      }),
    ),
  });
  const getPopupEndpoint = getPopupEndpointFactory({
    getPopupUseCase: auth(
      getPopupUseCaseFactory({
        popupService,
        userActivityRepository,
      }),
    ),
  });
  const updatePopupEndpoint = updatePopupEndpointFactory({
    updatePopupUseCase: pipe(
      auth,
      transacted,
    )(
      updatePopupUseCaseFactory({
        popupService,
      }),
    ),
  });
  const canRegisterEndpoint = canRegisterEndpointFactory({ regUseCase: canRegisterUseCaseFactory({ userRepository }) });
  const createGoogleAnalyticsEventEndpoint = createGoogleAnalyticsEventEndpointFactory({
    useCase: pipe(auth, transacted)(createGoogleAnalyticsEventUseCaseFactory({ userActivityRepository })),
  });
  const emailTestEndpoint = emailTestEndpointFactory({ emailTestUseCase: emailTestUseCaseFactory(mailService) });

  const getAllRewardsEndpoint = getAllRewardsEndpointFactory({
    getAllRewardsUseCase: pipe(auth, transacted)(getAllRewardsUseCaseFactory({ userRepository, referralService })),
  });

  const updateRewardEndpoint = updateRewardEndpointFactory({
    updateRewardUseCase: pipe(
      auth,
      transacted,
    )(
      updateRewardUseCaseFactory({
        userRepository,
        referralService,
        rewardRepository: referralRewardRepository,
        mailService,
        hmhBoxRequestEmail: config.hmhBoxRequestEmail,
        frontendDomain: config.frontendDomain,
        popupService,
      }),
    ),
  });

  const orderEkstraEndpoint = orderEkstraEndpointFactory({
    orderEkstraUseCase: pipe(
      auth,
      transacted,
    )(
      orderEkstraUseCaseFactory({
        mailService,
      }),
    ),
  });

  const startSalesProcessEndpoint = startSalesProcessEndpointFactory({
    startSalesProcessUseCase: pipe(
      auth,
      transacted,
    )(
      startSalesProcessUseCaseFactory({
        mailService,
      }),
    ),
  });

  const discussWithBroker = discussWIthBrokerEndpointFactory({
    discussWIthBrokerUseCase: pipe(
      auth,
      transacted,
    )(
      discussWithBrokerUseCaseFactory({
        mailService,
      }),
    ),
  });

  const getEstateStatisticsEndpoint = getEstateStatisticsEndpointFactory({
    useCase: auth(
      getEstateStatisticsUseCaseFactory({
        mongoEstateRepository: estateMongooseRepository,
        statisticsService,
        hjemNoConfig: config.hjemNo,
      }),
    ),
  });

  const getChecklistBuyer = getEstateBuyerChecklistsEndpointFactory({
    getEstateChecklistsUseCase: auth(getEstateChecklistsUseCaseFactory({ estateChecklistService })),
  });

  const toggleEstateBuyerChecklist = toggleEstateBuyerChecklistTodoEndpointFactory({
    toggleEstateChecklistTodoUseCase: auth(
      toggleEstateChecklistTodoUseCaseFactory({ estateChecklistService, estateChecklistConnectionRepository }),
    ),
  });

  const createByggstartLead = createByggstartLeadEndpointFactory({
    createByggstartLeadUseCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
    )(
      createByggstartLeadUseCaseFactory({
        mailService,
        byggstartMailAddress: config.byggstartEmail,
        sendLeads: config.sendVitecLeads,
        userRepository,
        leadService,
        externalLeadAuditRepository,
      }),
    ),
    rateLimit: config.services.rateLimit,
  });

  const createElectricityLead = createElectricityLeadEndpointFactory({
    createElectricityLeadUseCase: pipe(apikeyAuth)(
      createElectricityLeadUseCaseFactory({
        electricityLeadService,
        otpService,
        estateService,
        buyerMongooseRepository,
        vitecService,
      }),
    ),
    rateLimit: config.services.rateLimit,
  });

  const createExponovaLead = createExponovaLeadEndpointFactory({
    createExponovaLeadUseCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
    )(
      createExponovaLeadUseCaseFactory({
        mailService,
        userActivityRepository,
        sendLeads: config.sendVitecLeads,
        exponovaMailAddress: config.exponovaEmail,
        userRepository,
        leadService,
        externalLeadAuditRepository,
      }),
    ),
    rateLimit: config.services.rateLimit,
  });

  const createSectorAlarmLead = createSectorAlarmLeadEndpointFactory({
    createSectorAlarmLeadUseCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
    )(
      createSectorAlarmLeadUseCaseFactory({
        userRepository,
        sectorAlarmService,
      }),
    ),
    rateLimit: config.services.rateLimit,
  });

  const headDocumentDownload = headDownloadDocumentEndpointFactory();

  const getOtpEndpoint = getOtpEndpointFactory({
    getOtpUseCase: pipe(notifyOnOtpFailure)(
      getOtpUseCaseFactory({
        otpService,
      }),
    ),
  });
  const createOtpEndpoint = createOtpEndpointFactory({
    createOtpUseCase: pipe(notifyOnOtpFailure)(
      createOtpUseCaseFactory({
        estateService,
        sellerRepository: sellerMongooseRepository,
        buyerRepository: buyerMongooseRepository,
        otpService,
      }),
    ),
  });
  const patchOtpEndpoint = updateOtpEndpointFactory({
    updateOtpUseCase: pipe(notifyOnOtpFailure)(
      updateOtpUseCaseFactory({
        otpRepository,
        otpService,
      }),
    ),
  });
  const addOtpParticipantEndpoint = createOtpParticipantEndpointFactory({
    createOtpParticipantUseCase: pipe(notifyOnOtpFailure)(
      createOtpParticipantUseCaseFactory({
        otpService,
      }),
    ),
  });
  const removeOtpParticipantEndpoint = deleteOtpParticipantEndpointFactory({
    deleteOtpParticipantUseCase: pipe(notifyOnOtpFailure)(
      deleteOtpParticipantUseCaseFactory({
        otpService,
      }),
    ),
  });

  const updateOtpParticipantEndpoint = updateOtpParticipantEndpointFactory({
    updateOtpParticipantUseCase: pipe(notifyOnOtpFailure)(
      updateOtpParticipantUseCaseFactory({
        otpService,
      }),
    ),
  });

  const signOtpPdfEndpoint = createOtpDocumentEndpointFactory({
    createOtpDocumentUseCase: pipe(notifyOnOtpFailure)(
      createOtpDocumentUseCaseFactory({
        estateService,
        frontendDomain: config.frontendDomain,
        idfyClient,
        overtakeProtocolDocumentService,
        overtakeOfEmptyEstateDocumentService,
        otpService,
        otpMeterRepo,
        vitecService,
      }),
    ),
  });

  const getSignPdfEndpoint = getOtpDocumentEndpointFactory({
    getOtpDocumentUseCase: pipe(notifyOnOtpFailure)(
      getOtpDocumentUseCaseFactory({
        otpService,
        idfyClient,
      }),
    ),
  });

  const finishOtpEndpoint = finalizeOtpEndpointFactory({
    finalizeOtpUseCase: pipe(notifyOnOtpFinalizeFailure)(
      finalizeOtpUseCaseFactory({
        otpService,
        otpRepository,
        vitecService,
        electricityLeadService,
        estateService,
        mailService,
        frontendDomain: config.frontendDomain,
        slackService,
        isRunningInProduction: config.environmentName === 'production',
        idfyClient,
        fileService,
        idfyService,
        buyerMongooseRepository,
      }),
    ),
  });

  const createOtpMeterEndpoint = createOtpMeterEndpointFactory({
    createOtpMeterUseCase: pipe(notifyOnOtpFailure)(
      createOtpMeterUseCaseFactory({
        otpService,
      }),
    ),
  });
  const updateOtpMeterEndpoint = updateOtpMeterEndpointFactory({
    updateOtpMeterUseCase: pipe(notifyOnOtpFailure)(
      updateOtpMeterUseCaseFactory({
        otpService,
      }),
    ),
  });
  const deleteOtpMeterEndpoint = deleteOtpMeterEndpointFactory({
    deleteOtpMeterUseCase: pipe(notifyOnOtpFailure)(
      deleteOtpMeterUseCaseFactory({
        otpService,
      }),
    ),
  });

  const resetOtpFormEndpoint = resetOtpFormEndpointFactory({
    createOtpUseCase: pipe(notifyOnOtpFailure)(
      createOtpUseCaseFactory({
        estateService,
        otpService,
        sellerRepository: sellerMongooseRepository,
        buyerRepository: buyerMongooseRepository,
      }),
    ),
    deleteOtpUseCase: pipe(notifyOnOtpFailure)(
      apikeyAuth(
        deleteOtpUseCaseFactory({
          estateService,
          otpService,
        }),
      ),
    ),
  });

  const vippsGetInfoEndpoint = vippsGetInfoEndpointFactory({
    vippsGetInfoUseCase: vippsGetInfoUseCaseFactory({ vippsService }),
  });
  const vippsRegisterEndpoint = vippsRegisterEndpointFactory({
    registerUseCase: notifyOnRegistrationFailure(
      registrationUseCaseFactory({
        userRepository,
        userActivityRepository,
        userOptionsRepository,
        tokenService,
        estateService,
        mailService,
        slackService,
      }),
      RegisteredWith.VIPPS,
    ),
    vippsService,
  });
  const vippsLoginEndpoint = vippsLoginEndpointFactory({
    vippsLoginUseCase: vippsLoginUseCaseFactory({
      userActivityRepository,
      userRepository,
      popupService,
      tokenService,
      estateSyncService,
    }),
    vippsService,
  });

  const getEstateBuyersEndpoint = getEstateBuyersEndpointFactory({
    useCase: getEstateBuyersUseCaseFactory({ estateService, buyerRepository: buyerMongooseRepository }),
  });

  const listFeedEndpoint = listFeedEndpointFactory({ useCase: auth(listFeedUseCaseFactory({ feedRepository })) });

  const deleteFeedEndpoint = deleteFeedEndpointFactory({ useCase: auth(deleteFeedUseCaseFactory({ feedRepository })) });

  const setUserIosTokenEndpoint = setUserIosTokenEndpointFactory({
    useCase: auth(setUserIosTokenUseCase),
  });

  const deleteUserIosTokenEndpoint = deleteUserIosTokenEndpointFactory({
    useCase: auth(deleteUserIosTokenUseCase),
  });

  const setUserAndroidTokenEndpoint = setUserAndroidTokenEndpointFactory({
    useCase: auth(setUserAndroidTokenUseCase),
  });

  const deleteUserAndroidTokenEndpoint = deleteUserAndroidTokenEndpointFactory({
    useCase: auth(deleteUserAndroidTokenUseCase),
  });

  const sendSignSmsEndpoint = sendOtpSignSmsEndpointFactory({
    sendOtpSignSmsUseCase: sendOtpSignSmsUseCaseFactory({
      otpService,
      smsService,
      domain: config.domain,
      urlRepository,
      idfyClient,
      estateMongoRepo: estateMongooseRepository,
    }),
  });

  const getShortUrlEndpoint = getShortUrlEndpointFactory({ useCase: getShortUrlUseCaseFactory({ urlRepository }) });
  const createShortUrlEndpoint = createShortUrlEndpointFactory({
    useCase: apikeyAuth(shortenUrlUseCaseFactory({ urlRepository })),
  });
  const estateSyncBankIdEndpoint = estateSyncBankIdEndpointFactory({
    estateSyncBankIdUseCase: auth(
      estateSyncBankIdUseCaseFactory({
        idfyService,
        userRepository,
        estateSyncService,
      }),
    ),
  });
  const estateSyncBankIdForceEndpoint = estateSyncBankIdForceEndpointFactory({
    estateSyncBankIdForceUseCase: apikeyAuth(
      estateSyncBankIdForceUseCaseFactory({
        userRepository,
        estateSyncService,
      }),
    ),
  });

  const estateSyncVippsEndpoint = estateSyncVippsEndpointFactory({
    estateSyncVippsUseCase: auth(
      estateSyncVippsUseCaseFactory({
        vippsService,
        userRepository,
        estateSyncService,
      }),
    ),
  });

  const getSettlementSellerEndpoint = getSettlementSellerEndpointFactory({
    useCase: pipe(notifyOnSettlementSellerFailure)(
      getSettlementSellerUseCaseFactory({ settlementSellerRepository, estateMongooseRepository }),
    ),
  });

  const createSettlementSellerEndpoint = createSettlementSellerEndpointFactory({
    useCase: pipe(
      notifyOnSettlementSellerFailure,
      transacted,
    )(
      createSettlementSellerUseCaseFactory({
        settlementSellerRepository,
        estateService,
        sellerMongooseRepository,
        proxyMongooseRepository,
      }),
    ),
  });

  const updateSettlementSellerEndpoint = updateSettlementSellerEndpointFactory({
    useCase: pipe(
      notifyOnSettlementSellerFailure,
      transacted,
    )(updateSettlementSellerUseCaseFactory({ settlementSellerRepository })),
  });

  const updateSettlementSellerLoansEndpoint = updateSettlementSellerLoansEndpointFactory({
    useCase: pipe(
      notifyOnSettlementSellerFailure,
      transacted,
    )(updateSettlementSellerLoansUseCaseFactory({ settlementSellerRepository })),
  });

  const updateSettlementSellerAccountsEndpoint = updateSettlementSellerAccountsEndpointFactory({
    useCase: pipe(
      notifyOnSettlementSellerFailure,
      transacted,
    )(updateSettlementSellerAccountsUseCaseFactory({ settlementSellerRepository })),
  });

  const createIdfySellerDocumentEndpoint = createSettlementSellerDocumentEndpointFactory({
    useCase: pipe(notifyOnSettlementSellerFailure)(
      createSettlementSellerDocumentUseCaseFactory({
        estateService,
        frontendDomain: config.frontendDomain,
        idfyClient,
        settlementSellerRepository,
        settlementSellerDocumentService,
        fileService,
        vitecService,
      }),
    ),
  });

  const finalizeSettlementSellerEndpoint = finalizeSettlementSellerEndpointFactory({
    useCase: pipe(
      notifyOnSettlementSellerFinalizeFailure,
      optionalAuth,
    )(
      finalizeSettlementSellerUseCaseFactory({
        estateService,
        frontendDomain: config.frontendDomain,
        idfyClient,
        mailService,
        settlementSellerRepository,
        slackService,
        settlementService,
        fileService,
        idfyService,
      }),
    ),
  });

  const getSettlementSellerDocumentSignStatusEndpoint = getSettlementSellerDocumentSignStatusEndpointFactory({
    useCase: pipe(notifyOnSettlementSellerFailure)(
      getSettlementSellerDocumentSignStatusUseCaseFactory({ idfyClient, settlementSellerRepository }),
    ),
  });

  const sendSettlementSellerSignSmsEndpoint = sendSettlementSellerSignSmsEndpointFactory({
    useCase: pipe(notifyOnSettlementSellerFailure)(
      sendSettlementSellerSignSmsUseCaseFactory({
        domain: config.domain,
        idfyClient,
        settlementSellerRepository,
        smsService,
        urlRepository,
        estateMongoRepo: estateMongooseRepository,
      }),
    ),
  });

  const createSettlementBuyerEndpoint = createSettlementBuyerEndpointFactory({
    useCase: pipe(
      notifyOnSettlementBuyerFailure,
      transacted,
    )(
      createSettlementBuyerUseCaseFactory({
        settlementBuyerRepository,
        estateService,
        buyerMongooseRepository,
        pepParticipantRepo: pepFormParticipantRepository,
        proxyMongooseRepository,
      }),
    ),
  });

  const updateSettlementBuyerEndpoint = updateSettlementBuyerEndpointFactory({
    useCase: pipe(
      notifyOnSettlementBuyerFailure,
      transacted,
    )(updateSettlementBuyerUseCaseFactory({ settlementBuyerRepository })),
  });

  const createIdfyBuyerDocumentEndpoint = createSettlementBuyerDocumentEndpointFactory({
    useCase: pipe(notifyOnSettlementBuyerFailure)(
      createSettlementBuyerDocumentUseCaseFactory({
        estateService,
        frontendDomain: config.frontendDomain,
        idfyClient,
        settlementBuyerDocumentService,
        settlementBuyerRepository,
        vitecService,
      }),
    ),
  });

  const finalizeSettlementBuyerEndpoint = finalizeSettlementBuyerEndpointFactory({
    useCase: pipe(
      notifyOnSettlementBuyerFinalizeFailure,
      optionalAuth,
    )(
      finalizeSettlementBuyerUseCaseFactory({
        estateService,
        frontendDomain: config.frontendDomain,
        idfyClient,
        mailService,
        settlementBuyerRepository,
        settlementService,
        slackService,
        fileService,
        idfyService,
      }),
    ),
  });

  const getSettlementBuyerDocumentSignStatusEndpoint = getSettlementBuyerDocumentSignStatusEndpointFactory({
    useCase: pipe(notifyOnSettlementBuyerFailure)(
      getSettlementBuyerDocumentSignStatusUseCaseFactory({ idfyClient, settlementBuyerRepository }),
    ),
  });

  const sendSettlementBuyerSignSmsEndpoint = sendSettlementBuyerSignSmsEndpointFactory({
    useCase: pipe(notifyOnSettlementBuyerFailure)(
      sendSettlementBuyerSignSmsUseCaseFactory({
        domain: config.domain,
        idfyClient,
        settlementBuyerRepository,
        smsService,
        urlRepository,
        estateMongoRepo: estateMongooseRepository,
      }),
    ),
  });

  const getOtpMeterImageEndpoint = getOtpMeterImageEndpointFactory({
    getOtpMeterImageUseCase: getOtpMeterImageUseCaseFactory({
      fileService,
      otpService,
    }),
  });

  const getOtpParticipantImageEndpoint = getOtpParticipantImageEndpointFactory({
    getOtpParticipantImageUseCase: getOtpParticipantImageUseCaseFactory({
      fileService,
      otpService,
    }),
  });

  const getEkstraStatisticsEndpoint = getEkstraStatisticsEndpointFactory({
    useCase: auth(getEkstraStatisticsUseCaseFactory({ mongoEstateRepository: estateMongooseRepository })),
  });

  const createIdfyPEPDocumentEndpoint = createIdfyPEPDocumentEndpointFactory({
    createIdfyPEPFormDocumentUseCase: createIdfyPEPFormDocumentUseCaseFactory({
      estateService,
      frontendDomain: config.frontendDomain,
      idfyClient,
      pepDocumentService: politicallyExposedPersonDocumentService,
      pepFormRepository,
      pepParticipantRepository: pepFormParticipantRepository,
      vitecService,
    }),
  });

  const createPEPFormEndpoint = createPEPFormEndpointFactory({
    createPEPUseCase: pipe(notifyOnPEPFailure)(createPepFormUseCase),
  });

  const getPEPFormNewEndpoint = getPEPFormNewEndpointFactory({
    getPEPUseCase: getPEPFormUseCaseFactory({
      pepFormRepository,
      estateService,
    }),
  });

  const updatePEPFormNewEndpoint = updatePEPFormNewEndpointFactory({
    updatePEPFormUseCase: updatePEPFormUseCaseFactory({
      pepFormRepository,
    }),
  });

  const getPEPFormParticipantsEndpoint = getPEPFormParticipantsEndpointFactory({
    getPEPParticipantUseCase: getPEPFormParticipantUseCaseFactory({
      pepFormRepository,
      pepParticipantRepo: pepFormParticipantRepository,
    }),
  });

  const updatePEPFormParticipantEndpoint = updatePEPFormParticipantEndpointFactory({
    updatePEPParticipantUseCase: updatePEPFormParticipantUseCaseFactory({
      pepParticipantRepo: pepFormParticipantRepository,
    }),
  });

  const getPEPDocumentSignStatusNewEndpoint = getPEPDocumentSignStatusNewEndpointFactory({
    getPEPDocumentSignUrlUseCase: getPEPDocumentSignUrlUseCaseFactory({
      idfyClient,
      pepFormRepository,
      pepParticipantRepository: pepFormParticipantRepository,
      estateService,
    }),
  });

  const finalizePEPNewEndpoint = finalizePEPNewEndpointFactory({
    finalizePEPUseCase: pipe(notifyOnPEPFailure)(
      finalizePEPFormUseCaseFactory({
        estateService,
        frontendDomain: config.frontendDomain,
        idfyClient,
        mailService,
        pepFormRepository,
        pepParticipantRepository: pepFormParticipantRepository,
        fileService,
        slackService,
        idfyService,
        storebrandService,
        proxyMongooseRepository,
        sellerMongooseRepository,
      }),
    ),
  });

  const resetPepEndpoint = resetPepFormEndpointFactory({
    createPEPUseCase: pipe(notifyOnPEPFailure)(createPepFormUseCase),
    deletePEPUseCase: pipe(notifyOnPEPFailure)(
      apikeyAuth(
        deletePEPFormUseCaseFactory({
          estateService,
          pepParticipantRepo: pepFormParticipantRepository,
          pepFormRepository,
        }),
      ),
    ),
  });

  const getIFInsuranceEndpoint = getIfInsuranceOfferEndpointFactory({
    useCase: pipe(
      notifyOnLeadSendingSuccessOrFailure,
      optionalAuth,
      transacted,
    )(getIfInsuranceOfferUseCaseFactory({ serviceOfferService })),
    rateLimitConfig: config.services.rateLimit,
  });

  const lipscoreBrokerRatingAverageEndpoint = getLipscoreBrokerRatingAverageEndpointFactory({
    useCase: getLipscoreBrokerRatingAverageUseCaseFactory({ lipscoreBrokerRatingService }),
  });

  const upsertEstateImageFromUserEndpoint = upsertEstateImageFromUserEndpointFactory({
    useCase: pipe(auth)(
      updateEstateImageFromUserUseCaseFactory({
        awsS3Config: config.s3Config,
        estateImageFromUserRepository,
        estateMongooseRepository,
        awsS3Service,
        estatePgRepository: estateRepository,
      }),
    ),
  });

  const getEstateImageFromUserEndpoint = getEstateImageFromUserEndpointFactory({
    useCase: pipe(auth)(
      getEstateImageFromUserUseCaseFactory({
        estateImageFromUserRepository,
        estateMongooseRepository,
        estatePgRepository: estateRepository,
      }),
    ),
  });

  const getEstatePriceHistoryEndpoint = getEstatePriceHistoryEndpointFactory({
    useCase: pipe(auth)(
      getEstatePriceHistoryUseCaseFactory({
        estatePriceHistoryRepository,
      }),
    ),
  });

  const deleteSettlementBuyerEndpoint = deleteSettlementBuyerEndpointFactory({
    useCase: pipe(apikeyAuth)(
      deleteSettlementBuyerUseCaseFactory({
        estateService,
        settlementBuyerRepository,
      }),
    ),
  });

  const deleteSettlementSellerEndpoint = deleteSettlementSellerEndpointFactory({
    useCase: pipe(apikeyAuth)(
      deleteSettlementSellerUseCaseFactory({
        estateService,
        settlementSellerRepository,
      }),
    ),
  });

  const resetSettlementBuyerEndpoint = resetSettlementBuyerFormEndpointFactory({
    deleteSettlementBuyerUseCase: pipe(apikeyAuth)(
      deleteSettlementBuyerUseCaseFactory({
        estateService,
        settlementBuyerRepository,
      }),
    ),
    createSettlementBuyerUseCase: createSettlementBuyerUseCaseFactory({
      buyerMongooseRepository,
      estateService,
      pepParticipantRepo: pepFormParticipantRepository,
      proxyMongooseRepository,
      settlementBuyerRepository,
    }),
  });

  const resetSettlementSellerEndpoint = resetSettlementSellerFormEndpointFactory({
    deleteSettlementSellerUseCase: pipe(apikeyAuth)(
      deleteSettlementSellerUseCaseFactory({
        estateService,
        settlementSellerRepository,
      }),
    ),
    createSettlementSellerUseCase: createSettlementSellerUseCaseFactory({
      estateService,
      proxyMongooseRepository,
      sellerMongooseRepository,
      settlementSellerRepository,
    }),
  });

  const getExternalLeadAuditsEndpoint = getExternalLeadAuditEndpointFactory({
    useCase: apikeyAuth(
      getExternalLeadAuditUseCaseFactoryFactory({
        externalLeadAuditRepository,
      }),
    ),
  });

  const getBrokerFormsForEstatesEndpoint = getBrokerFormsForEstatesEndpointFactory({
    useCase: apikeyAuth(
      getBrokerFormsForEstatesUseCaseFactory({
        estateService,
        frontendDomain: config.frontendDomain,
        settlementBuyerRepository,
        settlementSellerRepository,
      }),
    ),
  });

  const updateSettlementBuyerParticipantEndpoint = updateSettlementBuyerParticipantEndpointFactory({
    useCase: updateSettlementBuyerParticipantUseCase,
  });

  const getPriceGuessingEstateEndpoint = getPriceGuessingDailyEstateEndpointFactory({
    useCase: auth(
      getPriceGuessingDailyEstateUseCaseFactory({
        priceGuessingEstateRepo: priceGuessingEstateRepository,
        priceGuessingUserGuessRepo: priceGuessingUserGuessRepository,
        vitecEstateRepo: estateMongooseRepository,
        areaRepository: areaRepository,
      }),
    ),
  });

  const guessEstatePriceEndpoint = guessEstatePriceEndpointFactory({
    useCase: auth(
      submitNewGuessForPriceGuessingEstateUseCaseFactory({
        priceGuessingEstateRepo: priceGuessingEstateRepository,
        priceGuessingUserGuessRepo: priceGuessingUserGuessRepository,
      }),
    ),
  });

  const getUserStatisticsEndpoint = getUserStatisticsEndpointFactory({
    useCase: auth(
      getPriceGuessingUserStatisticsUseCaseFactory({
        priceGuessingUserGuessRepo: priceGuessingUserGuessRepository,
      }),
    ),
  });

  const getPriceGuessingScoreboardEndpoint = getPriceGuessingScoreboardEndpointFactory({
    useCase: auth(
      getPriceGuessingScoreboardUseCaseFactory({
        priceGuessingUserGuessRepo: priceGuessingUserGuessRepository,
      }),
    ),
  });

  const getUserIdsWhoFinishedGuessingForTodayEndpoint = getUserIdsWhoFinishedGuessingForTodayEndpointFactory({
    useCase: getUserIdsWhoFinishedForTodayUseCaseFactory({
      priceGuessingEstateRepo: priceGuessingEstateRepository,
      priceGuessingUserGuessRepo: priceGuessingUserGuessRepository,
    }),
  });

  const getPriceGuessingReportEndpoint = getPriceGuessingReportEndpointFactory({
    useCase: getPriceGuessingReportUseCaseFactory({ priceGuessingUserGuessRepo: priceGuessingUserGuessRepository }),
  });

  const getConsentEndpoint = getConsentEndpointFactory({
    getConsentUseCase: getConsentUseCaseFactory({
      userRepository,
    }),
  });

  const updateConsentEndpoint = updateConsentEndpointFactory({
    updateConsentUseCase: pipe(apikeyAuth)(
      updateConsentUseCaseFactory({
        userRepository,
      }),
    ),
  });

  const updateConsentWithNordvikBoligCallbackEndpoint = updateConsentWithNordvikBoligCallbackEndpointFactory({
    updateConsentUseCase: updateConsentUseCaseFactory({
      userRepository,
    }),
  });

  const getTotalGuessesTodayByUserIdEndpoint = getTotalGuessesTodayByUserIdEndpointFactory({
    useCase: auth(
      getTotalGuessesTodayByUserIdUseCaseFactory({
        priceGuessingUserGuessRepo: priceGuessingUserGuessRepository,
      }),
    ),
  });

  const getSettlementBuyerEndpoint = getSettlementBuyerEndpointFactory({
    useCase: pipe(notifyOnSettlementBuyerFailure)(
      getSettlementBuyerUseCaseFactory({ settlementBuyerRepository, estateMongooseRepository, evApi: evRestApiClient }),
    ),
  });

  const endpoints = [
    deleteUserEndpoint,
    statusEndpoint,
    adminAuthEndpoint,
    adminInterfaceEndpoint,
    searchUsersEndpoint,
    impersonateUserEndpoint,
    authenticatedStatusEndpoint,
    getUserEndpoint,
    registerEndpoint,
    registerBrokerEndpoint,
    registerBankIdEndpoint,
    loginEndpoint,
    updateUserEndpoint,
    loginBrokerEndpoint,
    bankIdIframeEndpoint,
    getEstatesEndpoint,
    bankIdSessionEndpoint,
    getEstateEndpoint,
    createEstateEndpoint,
    getEstateTimelineSellerEndpoint,
    getEstateTimelineBuyerEndpoint,
    getTimelineEndpoint,
    getEstateTimelineEndpoint,
    getTimelineBucketEndpoint,
    getEstateTimelineBucketEndpoint,
    getEstateTodoEndpoint,
    getEstateValuationEndpoint,
    getEstatesValuationEndpoint,
    toggleEstateChecklistTodoEndpoint,
    setEstateEventDateEndpoint,
    getEstateChecklistsEndpoint,
    getSellersEndpoint,
    getBrokerEndpoint,
    getBrokerLeadsEndpoint,
    getBuyersEndpoint,
    deleteRecommendedNextActionEndpoint,
    getBrokerCustomerEndpoint,
    getOwnersEndpoints,
    getBrokerStats,
    getBrokerEstatesEndpoint,
    estateTimelineBrokerEndpoint,
    getBrokerEstatePotentialBuyersEndpoint,
    getSignableUserDocumentsCountEndpoint,
    getEstateDocumentsEndpoint,
    getSignableEstateDocumentsEndpoint,
    getPremarketEstatesEndpoint,
    getFavoritePremarketEstatesEndpoint,
    downloadDocumentEndpoint,
    getOfferEndpoint,
    getSignedUrlEndpoint,
    resetUserPasswordEndpoint,
    updateUserPasswordEndpoint,
    resetBrokerPasswordEndpoint,
    updateBrokerPasswordEndpoint,
    getEstateSalesEndpoint,
    verifyBrokerEmailEndpoint,
    getAreasEndpoint,
    getSmsVerificationEndpoint,
    verifySmsCodeEndpoint,
    updateEstateEndpoint,
    createVitecLeadEndpoint,
    changeUserPasswordEndpoint,
    changeBrokerPasswordEndpoint,
    updateUserEmailEndpoint,
    featureFlagEndpoint,
    addFavoriteEndpoint,
    deleteFavoriteEndpoiont,
    createStorebrandLeadEndpoint,
    createTelenorOfferEndpoint,
    isSteddyAvailableEndpoint,
    getFinancialDetailsEndpoint,
    updateFinancialDetailsEndpoint,
    getPopupEndpoint,
    updatePopupEndpoint,
    canRegisterEndpoint,
    createGoogleAnalyticsEventEndpoint,
    emailTestEndpoint,
    getAllRewardsEndpoint,
    updateRewardEndpoint,
    orderEkstraEndpoint,
    startSalesProcessEndpoint,
    discussWithBroker,
    getEstateStatisticsEndpoint,
    getChecklistBuyer,
    toggleEstateBuyerChecklist,
    createByggstartLead,
    createElectricityLead,
    createExponovaLead,
    createSectorAlarmLead,
    headDocumentDownload,
    getOtpEndpoint,
    createOtpEndpoint,
    patchOtpEndpoint,
    addOtpParticipantEndpoint,
    removeOtpParticipantEndpoint,
    updateOtpParticipantEndpoint,
    signOtpPdfEndpoint,
    getSignPdfEndpoint,
    finishOtpEndpoint,
    createOtpMeterEndpoint,
    deleteOtpMeterEndpoint,
    updateOtpMeterEndpoint,
    vippsGetInfoEndpoint,
    vippsRegisterEndpoint,
    vippsLoginEndpoint,
    getEstateBuyersEndpoint,
    listFeedEndpoint,
    deleteFeedEndpoint,
    setUserIosTokenEndpoint,
    deleteUserIosTokenEndpoint,
    setUserAndroidTokenEndpoint,
    deleteUserAndroidTokenEndpoint,
    sendSignSmsEndpoint,
    getShortUrlEndpoint,
    createShortUrlEndpoint,
    estateSyncBankIdEndpoint,
    estateSyncBankIdForceEndpoint,
    estateSyncVippsEndpoint,
    getTryggOfferEndpoint,
    getSsgOfferEndpoint,
    getStorebrandOfferEndpoint,
    getSteddyOfferEndpoint,
    getSettlementSellerEndpoint,
    createSettlementSellerEndpoint,
    updateSettlementSellerEndpoint,
    updateSettlementSellerLoansEndpoint,
    updateSettlementSellerAccountsEndpoint,
    createIdfySellerDocumentEndpoint,
    finalizeSettlementSellerEndpoint,
    getSettlementSellerDocumentSignStatusEndpoint,
    sendSettlementSellerSignSmsEndpoint,
    getSettlementBuyerEndpoint,
    createSettlementBuyerEndpoint,
    updateSettlementBuyerEndpoint,
    createIdfyBuyerDocumentEndpoint,
    finalizeSettlementBuyerEndpoint,
    getSettlementBuyerDocumentSignStatusEndpoint,
    sendSettlementBuyerSignSmsEndpoint,
    getEkstraStatisticsEndpoint,
    createPEPFormEndpoint,
    getPEPFormNewEndpoint,
    updatePEPFormNewEndpoint,
    getPEPFormParticipantsEndpoint,
    updatePEPFormParticipantEndpoint,
    getOtpMeterImageEndpoint,
    getOtpParticipantImageEndpoint,
    getPEPDocumentSignStatusNewEndpoint,
    createIdfyPEPDocumentEndpoint,
    finalizePEPNewEndpoint,
    getIFInsuranceEndpoint,
    lipscoreBrokerRatingAverageEndpoint,
    upsertEstateImageFromUserEndpoint,
    getEstateImageFromUserEndpoint,
    getEstatePriceHistoryEndpoint,
    getExternalLeadAuditsEndpoint,
    deleteSettlementBuyerEndpoint,
    deleteSettlementSellerEndpoint,
    resetSettlementBuyerEndpoint,
    resetSettlementSellerEndpoint,
    getBrokerFormsForEstatesEndpoint,
    updateSettlementBuyerParticipantEndpoint,
    getPriceGuessingEstateEndpoint,
    guessEstatePriceEndpoint,
    resetOtpFormEndpoint,
    resetPepEndpoint,
    getUserStatisticsEndpoint,
    getPriceGuessingScoreboardEndpoint,
    getUserIdsWhoFinishedGuessingForTodayEndpoint,
    getPriceGuessingReportEndpoint,
    getConsentEndpoint,
    updateConsentEndpoint,
    updateConsentWithNordvikBoligCallbackEndpoint,
    getTotalGuessesTodayByUserIdEndpoint,
  ];

  const securityRequirementObjects: OpenAPIV3.SecurityRequirementObject[] = [
    {
      bearerToken: [],
    },
  ];

  const components: OpenAPIV3.ComponentsObject = {
    schemas: {},
    securitySchemes: {
      bearerToken: {
        type: 'http',
        scheme: 'bearer',
      },
    },
  };

  const tags = [statusOATag];

  const swaggerOptions = fastifySwaggerFactory({
    tags,
    components,
    security: securityRequirementObjects,
    host: config.domainSwagger,
  });

  const server = fastifyServerFactory({
    swaggerOptions,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    endpoints: endpoints as Endpoint<Request<any, any, any, any>, Response<any, any>>[],
    logger,
    isFeatureEnabled: (x) => isEnabled(x),
  });

  server.addHook('onClose', async () => {
    console.log('sequelize connection is closing...');
    await sequelize.close();
    console.log('sequelize connection has been closed, mongoose connection is closing...');
    await mongooseConnection.close();
    console.log('mongoose connection has been closed.');
  });

  return server;
};

export const start = async ({ config, logger }: { config: ConfigObject; logger: Logger }): Promise<void> => {
  const getUnleashConfig = (): UnleashConfig => {
    const result: UnleashConfig = {
      appName: config.unleash.appName,
      url: config.unleash.url,
      instanceId: config.unleash.instanceId,
    };

    if (config.unleash.authorizationHeader) {
      result.customHeaders = {
        Authorization: config.unleash.authorizationHeader,
      };
    }
    return result;
  };

  const unleash = initialize(getUnleashConfig());
  unleash.on('ready', () => {
    logger.info(`Unleash heartbeat: ${isEnabled('sys_heartbeat').toString()}`);
  });
  unleash.on('error', (err) => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    logger.error(err, 'Error during unleash initialization.');
  });

  const vitecService = vitecServiceFactory({
    apiURL: config.vitec.apiURL,
    installationID: config.vitec.installationID,
    username: config.vitec.username,
    password: config.vitec.password,
  });
  const idfyClient = new IdfyClient(config.idfy.id, config.idfy.secret, [
    'document_read',
    'document_file',
    'document_write',
    'identify',
  ]);

  const hjemNoService = hjemNoServiceFactory({
    config: config.hjemNo,
    logger,
  });

  const statisticsService = statisticsServiceFactory({
    hjemNoService,
    hjemNoConfig: config.hjemNo,
  });

  const idfyService = idfyServiceFactory({
    domain: config.frontendDomain,
    config: config.idfy,
    vitecService,
    idfyClient,
  });

  const eiendomsverdiAuditRepository = eiendomsverdiAuditRepositoryFactory();
  const eiendomsverdiEstateCacheRepository = eiendomsverdiEstateCacheRepositoryFactory();

  const eiendomsverdiSoapApiClientService = createEiendomsverdiSoapApiClient(
    axios,
    config.eiendomsverdi.serviceUrl,
    config.eiendomsverdi.user,
    config.eiendomsverdi.password,
    'Service',
    eiendomsverdiAuditRepository,
  );
  const fetchCachedEstateDataService = fetchCachedEstateDataFactory({
    cacheRepository: eiendomsverdiEstateCacheRepository,
    soapApiClient: eiendomsverdiSoapApiClientService,
    config: config,
  });
  const evRestApiClient = createEiendomsverdiRestApiClient({
    axios,
    tokenUrl: config.eiendomsverdi.tokenUrlRest,
    clientId: config.eiendomsverdi.clientIdRest,
    clientSecret: config.eiendomsverdi.clientSecretRest,
    apiUrl: config.eiendomsverdi.apiUrlRest,
    estimateApiUrl: config.eiendomsverdi.estimateApiUrlRest,
  });
  const fetchCachedRestEstateDataService = cacheRestRequestServiceFactory({
    cacheRepository: eiendomsverdiEstateCacheRepository,
    evRestApiClient,
    evConfig: config.eiendomsverdi,
  });
  const estateValuationService = createEiendomsverdiEstateValuationService(
    createEiendomsverdiSoapApiClient(
      axios,
      config.eiendomsverdi.publicInformationServiceUrl,
      config.eiendomsverdi.user,
      config.eiendomsverdi.password,
      'PublicInformationRealtime',
      eiendomsverdiAuditRepository,
    ),
    cacheRequestServiceFactory({
      config,
      fetchCachedEstateData: fetchCachedEstateDataService,
      soapApiClient: eiendomsverdiSoapApiClientService,
    }),
    evRestApiClient,
    fetchCachedRestEstateDataService,
    unleash,
  );

  const server = await create({
    config,
    logger,
    vitecService,
    statisticsService,
    idfyService,
    idfyClient,
    valuationService: estateValuationService,
    unleash,
  });

  await server.listen(config.port, '0.0.0.0');
  logger.info(`app listening on port ${config.port}`);
  server.swagger();
};

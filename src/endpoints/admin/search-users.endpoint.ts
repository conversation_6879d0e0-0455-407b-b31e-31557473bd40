import type { Endpoint } from '../../framework/endpoint/endpoint';
import { EndpointMethod } from '../../framework/endpoint/endpoint';
import type { Request, Response } from '../../framework/endpoint/request-response';
import type { AsyncUseCase } from '../../framework/use-case/async.use-case';
import type { WithApiKey } from '../../use-cases/apikey-authenticated.use-case';

type SearchUsersRequest = Request<
  Record<string, never>,
  { query: string; limit?: number },
  Record<string, never>,
  Record<string, string>
>;

type UserSearchResult = {
  id: string;
  email: string;
  name: string;
  phoneNumber: string;
  passwordCode: string;
};

type SearchUsersResponse = Response<{ users: UserSearchResult[] }, Record<string, never>>;
type SearchUsersEndpoint = Endpoint<SearchUsersRequest, SearchUsersResponse>;

type SearchUsersEndpointFactory = (params: {
  useCase: AsyncUseCase<WithApiKey<{ query: string; limit: number }>, UserSearchResult[]>;
  rateLimit: { max: number; timeWindow: number };
}) => SearchUsersEndpoint;

const getApiKeyFromHeader = (headers: Record<string, string | string[]>): string => {
  const apiKey = headers['x-api-key'];
  if (!apiKey || Array.isArray(apiKey)) {
    return '';
  }
  return apiKey;
};

export const searchUsersEndpointFactory: SearchUsersEndpointFactory = ({ useCase, rateLimit }) => ({
  method: EndpointMethod.GET,
  route: '/admin/users/search',
  schema: {
    summary: 'Search users for impersonation',
    description: 'Search users by email, name, or phone number for admin impersonation',
    tags: ['Admin'],
    headers: {
      type: 'object',
      properties: {
        'x-api-key': { type: 'string' },
      },
      required: ['x-api-key'],
    },
    querystring: {
      type: 'object',
      properties: {
        query: { type: 'string', minLength: 2 },
        limit: { type: 'number', minimum: 1, maximum: 50, default: 10 },
      },
      required: ['query'],
    },
    response: {
      200: {
        type: 'object',
        properties: {
          users: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string' },
                phoneNumber: { type: 'string' },
                passwordCode: { type: 'string' },
              },
            },
          },
        },
      },
      401: {
        type: 'object',
        properties: {
          error: { type: 'string' },
        },
      },
    },
  },
  handler: async (request) => {
    const apiKey = getApiKeyFromHeader(request.headers);
    const { query, limit = 10 } = request.query;

    const result = await useCase({
      apiKey,
      query,
      limit,
    });

    return {
      status: 200,
      response: { users: result },
      headers: {},
    };
  },
  rateLimit,
});

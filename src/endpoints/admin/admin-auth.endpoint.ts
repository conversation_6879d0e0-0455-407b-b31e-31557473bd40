import type { Endpoint } from '../../framework/endpoint/endpoint';
import { EndpointMethod } from '../../framework/endpoint/endpoint';
import type { Request, Response } from '../../framework/endpoint/request-response';

type AdminAuthRequest = Request<
  Record<string, never>,
  Record<string, never>,
  Record<string, never>,
  Record<string, string>
>;
type AdminAuthResponse = Response<{ authenticated: boolean } | { error: string }, Record<string, never>>;
type AdminAuthEndpoint = Endpoint<AdminAuthRequest, AdminAuthResponse>;

type AdminAuthEndpointFactory = (params: { adminApiKey: string }) => AdminAuthEndpoint;

const getApiKeyFromHeader = (headers: Record<string, string | string[]>): string => {
  const apiKey = headers['x-api-key'];
  if (!apiKey || Array.isArray(apiKey)) {
    return '';
  }
  return apiKey;
};

export const adminAuthEndpointFactory: AdminAuthEndpointFactory = ({ adminApiKey }) => ({
  method: EndpointMethod.POST,
  route: '/admin/auth',
  schema: {
    summary: 'Authenticate admin user',
    description: 'Validates admin API key for accessing admin functions',
    tags: ['Admin'],
    headers: {
      type: 'object',
      properties: {
        'x-api-key': { type: 'string' },
      },
      required: ['x-api-key'],
    },
    response: {
      200: {
        type: 'object',
        properties: {
          authenticated: { type: 'boolean' },
        },
      },
      401: {
        type: 'object',
        properties: {
          error: { type: 'string' },
        },
      },
    },
  },
  handler: async (request) => {
    const apiKey = getApiKeyFromHeader(request.headers);

    if (apiKey === adminApiKey) {
      return {
        status: 200,
        response: { authenticated: true },
        headers: {},
      };
    }

    return {
      status: 401,
      response: { error: 'Invalid API key' },
      headers: {},
    };
  },
});
